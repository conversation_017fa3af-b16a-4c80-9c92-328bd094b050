# -*- coding: utf-8 -*-
"""
配置文件
"""
import os
import json
from urllib.parse import urljoin

NAME = "booking-new-automatic-spider"
PROJECT_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
PLUGIN_JS = os.path.join(PROJECT_PATH, "docs", "stealth.min.js")        # 消除selenium标记

SERVER_ENV = os.getenv('SERVER_ENV', 'dev')

# 录屏开关
RECORD_STATE = True if os.getenv("RECORD_STATE", "false") == "true" else False

ENV_PREFIX = {'beta': 'beta-', 'dev': 'dev-', 'local': 'dev-', 'prod': '', "uat": "uat-"}.get(SERVER_ENV, 'dev-')  # 接口前缀

BOOKING_CONFIG = json.loads(open(os.path.join(PROJECT_PATH, 'conf', 'config_base.json'), 'r+').read())
# redis配置
REDIS_CONFIG = BOOKING_CONFIG["redis_config"][SERVER_ENV]
# 邮件配置
MAIL_CONFIG = BOOKING_CONFIG['mail_config']
# mysql 配置
SPIDER_MYSQL_CONFIG = BOOKING_CONFIG['spider_mysql_config'][SERVER_ENV]
# 斐斐打码 配置
FFDM_CONFIG = BOOKING_CONFIG['ffdm']
# -----------------------------------------------duckduckgo-----------------------------------------------
ACCOUNT_CONFIG = BOOKING_CONFIG['account']

# 视频上传api
nas_upload_api = BOOKING_CONFIG["nas_upload_api"]
NAS_URL = nas_upload_api[SERVER_ENV]
# 下载api
nas_download_api = BOOKING_CONFIG["nas_download_api"]
NAS_DOWNLOAD_URL = nas_download_api[SERVER_ENV]
# 视频链接
NAS_PREVIEW_URL = f'https://{ENV_PREFIX}fileview.hgj.com/onlinePreview?url='

# rocketmq
ROCKET_MQ_CONFIG = BOOKING_CONFIG['rocketmq']  # rocketMQ 配置

MQ_SUBSCRIBE_ID = NAME
MQ_TOPIC = 'smart-booking-python-booking-topic'     # 订阅topic，获取消息
MQ_SUBSCRIBE_TAG = "python-do-booking"              # 订阅的tag

MQ_PRODUCER_ID = 'booking-spider'  # 生产者

MQ_CREATE_BILL_NO_TAG = 'python-to-booking-result'  # 回填提单号的tag
MQ_UPDATE_BILL_NO_TAG = 'python-to-booking-bill-no-update'  # 回填船公司提单号

# 海管家箱型code - inttra 箱型 显示
CONTAINER_TYPE_HGJ_2_INTTRA = {
    '45NOR': '40 Reefer High Cube (45R1)',
    '40NOR': '40 Reefer High Cube (45R1)',
    '45HC': '45 High Cube (L5G0)',
    '20GP': '20 Standard Dry (22G0)',
    '40GP': '40 Standard Dry (42G0)',
    '40HC': '40 High Cube (45G0)',
    '40HQ': '40 High Cube (45G0)',
}

# INTTRA支持的船司
INTTRA_SUPPORT_CARRIER_LIST = ['CMA', 'CNC', 'ANL', "MSC", "HLC"]
# 华东外运支持的航司
HD_SUPPORT_CARRIER_LIST = ["MSC", "TSL", "SITC"]

# aksk
AKSK = {
    "video": {
        "local": {"ACCESS_KEY": "a547f585d9df4104b5812771ec0f1dce", "SECRET_KEY": "5678b65d314670aa476f61221f568c9fb1c254e0a1feb23719ad0fd751a1d044"},
        "dev": {"ACCESS_KEY": "a547f585d9df4104b5812771ec0f1dce", "SECRET_KEY": "5678b65d314670aa476f61221f568c9fb1c254e0a1feb23719ad0fd751a1d044"},
        "beta": {"ACCESS_KEY": "a547f585d9df4104b5812771ec0f1dce", "SECRET_KEY": "5678b65d314670aa476f61221f568c9fb1c254e0a1feb23719ad0fd751a1d044"},
        "prod": {"ACCESS_KEY": "fae4d6825c3f43959e5fb539067bc032", "SECRET_KEY": "b5bb662857c752d88c3c5d5c4064c822e17c074e94d5c47f4c8689281d5189c5"},
    }.get(SERVER_ENV, "dev"),
    "booking": {
        "local": {"ACCESS_KEY": "a547f585d9df4104b5812771ec0f1dce", "SECRET_KEY": "5678b65d314670aa476f61221f568c9fb1c254e0a1feb23719ad0fd751a1d044"},
        "dev": {"ACCESS_KEY": "a547f585d9df4104b5812771ec0f1dce", "SECRET_KEY": "5678b65d314670aa476f61221f568c9fb1c254e0a1feb23719ad0fd751a1d044"},
        "beta": {"ACCESS_KEY": "a547f585d9df4104b5812771ec0f1dce", "SECRET_KEY": "5678b65d314670aa476f61221f568c9fb1c254e0a1feb23719ad0fd751a1d044"},
        "prod": {"ACCESS_KEY": "87a7db5f199a4be4ac03d2473f57b59a", "SECRET_KEY": "9c859cf8c7e4dad0fc38b5fb7bc8c3e65326ed70508c72bf96bbee96fa51e60f"},
    }.get(SERVER_ENV, "dev")
}

# 船司订舱数据校验API
check_booking_url = BOOKING_CONFIG["check_booking_data_api"]
HGJ_API_URL = f"{check_booking_url[SERVER_ENV]}/booking-open-order/aksk/bookingManage/python/queryBookingInfo"
# 获取订舱数据查看放舱状态
HGJ_API_STATE_URL = f"{check_booking_url[SERVER_ENV]}/booking-open-order/aksk/bookingManage/python/queryBookingPageInfoOfPython"
# 上传放舱结果
UPLOAD_SEARCH_RESULT_API = f"{check_booking_url[SERVER_ENV]}/booking-common-channel/aksk/python/operate"
# 获取inttra号接口
HGJ_BOOKING_ORIGIN_BILL_NO_API = f"{check_booking_url[SERVER_ENV]}/booking-open-order/aksk/bookingManage/python/queryOrderForLara/SENT_TO_SHIPPING_COMPANY_FOR_RELEASE"
# 拒绝订舱接口
CANCEL_API = f"{check_booking_url[SERVER_ENV]}/booking-common-channel/aksk/rpa/execute/refuseBooking"

# 订舱的包装单位API
package_unit_url = BOOKING_CONFIG["get_package_unit_api"]
PACKAGE_API_URL = f"{package_unit_url[SERVER_ENV]}/booking/order/pass/package/findByCode?"

# 错误预警群
DEVELOPER_NOTIFY_API = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ec616b74-b95c-4c59-b46d-abefe95972be"
# 订舱组
BOOKING_GROUP_API = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c76c7c8c-29d6-4e48-8750-81d3eb026745"
# gunicorn配置文件路径，方便将不是里外启动的服务日志输出
GUNICORN_FILENAME = os.path.join(PROJECT_PATH, "gunicorn.conf.py")
# 接入网站的信息
WEBSITE_INFO = {
    "shhh": {"website": "https://hhdc.chinasailing.com.cn/home", "website_name": "上海航华"},
    "inttra": {"website": "https://booking.inttra.e2open.com/create", "website_name": "inttra"},
    "MSC": {"website": "https://hhdc.chinasailing.com.cn/home", "website_name": "中国外运网上服务系统"},
    "SITC": {"website": "https://hhdc.chinasailing.com.cn/home", "website_name": "中国外运网上服务系统"},
    "TSL": {"website": "https://hhdc.chinasailing.com.cn/home", "website_name": "中国外运网上服务系统"},
    "CMA": {"website": "https://www.cma-cgm.com/", "website_name": "cma"}
    }

# 不填写海运附加费的企业
DONT_INSERT_ADDITIONAL_CHARGES_LIST = ["MSC"]

# 获取cookies接口前缀
COOKIE_URL_PREFIX = {
    "dev": "https://dev-apisix.hgj.com",
    "beta": "https://beta-apisix.hgj.com",
    "prod": "https://apisix.hgj.com"
}.get(SERVER_ENV, "beta")
GET_PROXY_URL = urljoin(COOKIE_URL_PREFIX, "/proxy-pool/get_ip")

GET_COOKIE_URL = urljoin(COOKIE_URL_PREFIX, "/account-pool/get_cookie")