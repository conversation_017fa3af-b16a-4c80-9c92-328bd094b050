{"spider_mysql_config": {"local": {"host": "dev-middle.hgj.net", "port": 3306, "user": "spider", "password": "spider", "db": "spider-service", "charset": "utf8"}, "dev": {"host": "dev-middle.hgj.net", "port": 3306, "user": "spider", "password": "spider", "db": "spider-service", "charset": "utf8"}, "beta": {"host": "beta-middle.hgj.net", "port": 3306, "user": "spider", "password": "spider", "db": "spider-service", "charset": "utf8"}, "uat": {"host": "*************", "port": 3306, "user": "spider", "password": "spider", "db": "spider-service", "charset": "utf8"}, "prod": {"host": "*************", "port": 3306, "user": "spider_service", "password": "fyie6Ev+S9bfmhbu", "db": "spider-service", "charset": "utf8"}}, "redis_config": {"local": {"host": "dev-middle.hgj.net", "port": 6379, "password": "123456", "db": 1, "max_connections": 1}, "dev": {"host": "dev-middle.hgj.net", "port": 6379, "password": "123456", "db": 1, "max_connections": 1}, "beta": {"host": "beta-middle.hgj.net", "port": 6379, "password": "123456", "db": 1, "max_connections": 1}, "prod": {"host": "************", "port": 6379, "password": "ahjlub7nk%wRosR0", "db": 0, "max_connections": 1}}, "mail_config": {"user": "<EMAIL>", "password": "Booking1@", "host": "smtp.yunlsp.com", "port": 465}, "ffdm": {"pd_id": "134262", "pd_key": "eRf6SGxMIklVipO+hY6RHrKMKq7uPD9S", "app_id": "334262", "app_key": "EVnmNMc9chmOCjtvxwBHU3E1AQSD4v+0"}, "rocketmq": {"local": "dev-middle.hgj.net:9876", "dev": "dev-middle.hgj.net:9876", "beta": "beta-rmq.hgj.net:9876", "uat": "************:9876", "prod": "rmq2.hgj.net:9876;rmq3.hgj.net:9876;rmq4.hgj.net:9876"}, "account": {"shhh": {"username": "SZHGJ", "password": "Yunlsp1357@"}, "old_shhh": {"username": "SZHGJ", "password": "SZhgj123456."}, "inttra": {"username": "<EMAIL>", "password": "Szhgj123@"}}, "check_booking_data_api": {"local": "https://dev-apisix.hgj.com", "dev": "https://dev-apisix.hgj.com", "beta": "https://beta-apisix.hgj.com", "uat": "http://uat-ingress-ng.hgj.com", "prod": "http://ingress-ng.hgj.com"}, "get_package_unit_api": {"local": "https://dev-ingress.hgj.com", "dev": "https://dev-ingress.hgj.com", "beta": "https://beta-apisix.hgj.com", "uat": "https://uat-ingress-ng.hgj.com", "prod": "https://ingress-ng.hgj.com"}, "nas_upload_api": {"local": "https://iter-apisix.hgj.com/whale-nas-manager-server/access/upload/security/v1", "dev": "https://iter-apisix.hgj.com/whale-nas-manager-server/access/upload/security/v1", "beta": "https://beta-apisix.hgj.com/whale-nas-manager-server/access/upload/security/v1", "uat": "https://uat-file-ingress.hgj.com/whale-nas-manager-server/access/upload/security/v1", "prod": "https://file-ingress.hgj.com/whale-nas-manager-server/access/upload/security/v1"}, "nas_download_api": {"local": "https://iter-apisix.hgj.com/whale-nas-manager-server/pass/proxy/", "dev": "https://iter-apisix.hgj.com/whale-nas-manager-server/pass/proxy/", "beta": "https://beta-apisix.hgj.com/whale-nas-manager-server/pass/proxy/", "uat": "https://uat-file-ingress.hgj.com/whale-nas-manager-server/pass/proxy/", "prod": "https://file-ingress.hgj.com/whale-nas-manager-server/pass/proxy/"}}