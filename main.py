# -*- coding: utf-8 -*-
import os
import time
from threading import Thread

from flask import abort, request

from server import app
from conf import PROJECT_PATH, SERVER_ENV
from spiders.eptrade.bind_team import HandleMQ
from spiders.eptrade.crawl_booking_info import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from utils.util_normal import listdir_nohidden, send_message_to_developer
from utils.util_scheduler import Scheduler
from spiders.inttra_booking.bill_no_query import Telescope
from spiders.auto_upload_bc.handle_data import HandleData
from spiders.auto_upload_bc.upload_bc import UploadBc

spider_folder_name = 'spiders'
check_folder_name = "check_booking"

check_booking_pyfile = [i for i in listdir_nohidden(os.path.join(PROJECT_PATH, spider_folder_name, check_folder_name)) if i.startswith("check_booking")]                                     # 校验文件夹
need_run_pyfile = [os.path.join(PROJECT_PATH, spider_folder_name, "booking_start.py")]


@app.route('/')
@app.route('/actuator/health')
def health():
    """健康检查"""
    return {"code": 200}


@app.before_first_request
def before_first_request():
    """第一次运行时的操作"""
    Thread(target=run_task).start()
    Thread(target=HandleMQ().go).start()


def run_task():
    contents = list()

    for pyfile in need_run_pyfile:
        command = f'nohup python3 {pyfile} 2>&1 &'
        app.logger.error(command)
        contents.append(SERVER_ENV + ' - ' + command)
        os.system(command)

    if contents:
        send_message_to_developer('\n'.join(contents).strip('\n'), [])


@app.route("/updateBillNo")
def update_bill_no():
    """更新提单号"""
    telescope = Telescope()
    Thread(target=telescope.main).start()
    return {"code": 200}


@app.route('/check-booking', methods=["GET", "POST"])
def check_booking():
    """
    启动订舱结果查询
    启动方式: 将check_booking下面的所有文件都进行启动
    暂停: 传入启动的停止的模块名称进行停止
    :return:
    """
    # 查看是否找到执行文件, 如果没有就报500错误
    if len(check_booking_pyfile) < 1:
        abort(500)
    if request.method == "GET":
        args = request.args
        name = args.get("name", "")
        pyfile = "".join(["check_booking_", name, ".py"])
        # 启动任务
        if pyfile in set(check_booking_pyfile):
            file_path = os.path.join(PROJECT_PATH, spider_folder_name, check_folder_name, pyfile)
            is_start = Scheduler().start(pyfile, file_path)
            if is_start:
                return {"code": 200}
            abort(400)
    elif request.method == "POST":
        name = request.form.get("name", "")
        name = name.split(".")[0]
        pyfile = "".join(["check_booking_", name, ".py"])
        if pyfile in set(check_booking_pyfile):
            res = Scheduler().stop(pyfile)
            if res:
                return {"code": 200, "message": f"{name}停止成功"}
            else:
                return {"code": 500, "message": f"{name}停止失败"}
        else:
            return {"code": 500, "message": f"{name}文件未找到", "fileList": [i.split(".")[0] for i in check_booking_pyfile]}
    else:
        abort(405)


@app.route("/getAttachment")
def get_attachment():
    """过滤数据，调度下载附件连接"""
    HandleData().filter_data()
    return {"code": 200, "message": "成功"}


@app.route("/updateBillNoState", methods=["POST"])
def update_state():
    """更新单号数据和状态"""
    json_data = request.get_json()
    HandleData().insert_data_to_mysql(json_data)
    return {"code": 200, "message": "成功"}


@app.route("/uploadBC")
def upload_cma_bc():
    """
    每隔一个小时上传一次当天的bc，补偿机制，
    查询的时间范围：
    开始时间：昨天凌晨
    结束时间：当前时间
    :return:
    """
    Thread(target=UploadBc().get_unsent_data, args=(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time() - 48*60*60)), time.strftime("%Y-%m-%d %H:%M:%S"))).start()
    return {"code": 200, "message": "成功"}


@app.route("/eptrade-getData")
def eptrade_get_data():
    """每半小时查询一次亿通数据"""
    Thread(target=GetDataSpider().main).start()
    return {"code": 200, "message": "成功"}

if __name__ == '__main__':
    app.run("0.0.0.0", 8090, debug=True)