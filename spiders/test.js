






<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head id="Head1">
    <title>亿通电子订舱平台</title>


	<link rel="stylesheet" type="text/css" href="/epb/css/default.css"  />
	<link rel="stylesheet" type="text/css" href="/epb/css/easyui/default/easyui.css" />
	<link rel="stylesheet" type="text/css" href="/epb/css/easyui/icon.css" />

	<script type="text/javascript" src="/epb/js/ebw/jquery-1.4.2.min.js"></script>
	<script type="text/javascript" src="/epb/js/ebw/jquery.easyui.min.js"></script>
	<script type="text/javascript" src="/epb/js/ebw/outlook2.js?version=201702221"> </script>


	<script type="text/javascript" >
	 var _menus = {
     "menus":[
          {"menuid":"1","icon":"icon-sys","menuname":"海运出口/EXPORT","menus":[ {"menuname":"换舱申请及查询/BKG SWAP(New request/Query)","isPortalPage":"N","icon":"icon-users","url":"bookSwapCabinList.html?cdBk=Y&ticket=8c65eed3-4fb6-4708-b708-672d5e192b4f"},{"menuname":"订舱查询/BKG QUERY","isPortalPage":"N","icon":"icon-users","url":"cdBookList.html?ticket=8c65eed3-4fb6-4708-b708-672d5e192b4f"},{"menuname":"预配舱单系统","isPortalPage":"Y","icon":"icon-users","url":"https://u.eptrade.cn/usercenter/login?ticket=8c65eed3-4fb6-4708-b708-672d5e192b4f"},{"menuname":"打印通知单","isPortalPage":"Y","icon":"icon-users","url":"https://u.eptrade.cn/usercenter/login?ticket=8c65eed3-4fb6-4708-b708-672d5e192b4f"},{"menuname":"VGM系统","isPortalPage":"Y","icon":"icon-users","url":"https://u.eptrade.cn/usercenter/login?ticket=8c65eed3-4fb6-4708-b708-672d5e192b4f"},{"menuname":"提单样本提交及查询/SI CREATION&QUERY","isPortalPage":"N","icon":"icon-users","url":"cdBlcList.html?ticket=8c65eed3-4fb6-4708-b708-672d5e192b4f"},{"menuname":"EDI报文导入","isPortalPage":"N","icon":"icon-users","url":"upload/uploadScBkIftmbf.html?ticket=8c65eed3-4fb6-4708-b708-672d5e192b4f"} ]	}
    ]};
     var userLoginId ='CN341827';
    //设置登录窗口
    function openPwd() {
        $('#w').window({
            title: '修改密码',
            width: 300,
            modal: true,
            shadow: true,
            closed: true,
            height: 200,
            resizable:false
        });
    }
    //关闭登录窗口
    function closePwd() {
        $('#w').window('close');
    }
    //修改密码
    function serverLogin() {
        var $newpass = $('#txtNewPass');
        var $oldpass = $('#txtOldPass');
        var $rePass = $('#txtRePass');
        if ($newpass.val() == '') {
            msgShow('系统提示', '请输入密码！', 'warning');
            return false;
        }
        if ($rePass.val() == '') {
            msgShow('系统提示', '请在一次输入密码！', 'warning');
            return false;
        }
        if ($newpass.val() != $rePass.val()) {
            msgShow('系统提示', '两次密码不一至！请重新输入', 'warning');
            return false;
        }
        $.post('cdus.html?method=updatePassword&newpass=' + $newpass.val()+'&oldpass=' + $oldpass.val(), function(data) {
              eval('data='+data);
                //alert(data);
                if (data.success){
                    $.messager.alert('修改成功','成功','success');
                    $('#tt').datagrid('reload');

                }else{
                    $.messager.alert('修改失败','出错','error');
                }
            //msgShow('系统提示', '恭喜，密码修改成功！<br>您的新密码为：' + msg, 'info');
            $newpass.val('');
            $rePass.val('');
            //close();
        })
        closePwd()
    }

    $(function() {
        sessionStorage.setItem("userLoginId", userLoginId);
        $('#w').css("display","");
        openPwd();
        //
        $('#editpass').click(function() {
            $('#w').window('open');
        });

        $('#btnEp').click(function() {
            serverLogin();
        })

        $('#btnCancel').click(function(){closePwd();})



        $('#loginOut').click(function() {
            $.messager.confirm('系统提示', '您确定要退出本次登录吗?', function(r) {

                if (r) {
                    location.href = "/epb/logout/logout.html";
                }
            });

        })
        $('#loginOutOne').click(function() {
            var email = '';
            $.messager.confirm('系统提示', '您确定要退出本次登录吗?', function(r) {
                if (r) {
                    $.post('/epb/login/resetVeriCode.html',
                        {'email':email}
                        ,
                        function(data){
                            eval('data='+data);
                        });
                    location.href = "/epb/login/login_one.html";
                }
            });
        })

        $('#loginOutOneCdbook').click(function() {
            var email = '';
            $.messager.confirm('系统提示', '您确定要退出本次登录吗?', function(r) {
                if (r) {
                    $.post('/epb/login/resetVeriCode.html',
                        {'email':email}
                        ,
                        function(data){
                            eval('data='+data);
                        });
                    location.href = "/epb/login/scno_direct_bk.html";
                }
            });
        })
        $('#loginOutOneScDirbook').click(function() {
            $.messager.confirm('系统提示', '您确定要退出本次登录吗?', function(r) {
              if (r) {
                location.href = "/epb/login/scno_direct_bk.html";
              }
            });
        })
        $('#help a').click(function() {
            $.messager.alert('提示信息','正在建设中...','info');
        });
        $('#suggest').click(function() {
            //$.messager.alert('提示信息','正在建设中...','info');
            window.addNewTab('意见反馈',"help/feedback.html");
        });
        $('#case a').click(function() {
            $.messager.alert('提示信息','正在建设中...','info');
        });

    });

	function animateMenu(){
		$("body").layout('collapse','west');
	}

    function bookingSearch(){
        location.href = "/epb/individualBookList.html";




    }

    function bookingConfirm(){
        location.href = "/epb/indivBlcList.html";
    }


	</script>

	<style type="text/css">
	 .navspan{
	 	color: red;
		text-decoration: underline;
		cursor:pointer;
	 }zho
	</style>

</head>
<body class="easyui-layout" style="overflow-y: hidden"  scroll="no">
<noscript>
<div style=" position:absolute; z-index:100000; height:2046px;top:0px;left:0px; width:100%; background:white; text-align:center;">
    <img src="images/noscript.gif" alt='抱歉，请开启脚本支持！' />
</div></noscript>


    <div region="north" split="true" border="false" style="overflow: hidden; height: 30px;
        background: url(images/layout-browser-hd-bg.gif) #7f99be repeat-x center 50%;
        line-height: 20px;color: #fff; font-family: Verdana, 微软雅黑,黑体">
        <!--  <span style="padding-left:10px; font-size: 16px; " class="head"><img src="images/blocks.gif" width="20" height="20" align="bottom" /> 亿通订舱系统</span>-->
        <span style="float:left; padding-right:20px; font-size: 16px;" class="head" id="msgspan1">

                <img src="images/blocks.gif"  height="20" align="bottom" />
                亿通电子订舱平台

        </span>


        <span style="float:right; padding-right:20px;" class="head" >

            当前用户： 苏州海管家物流科技有限公司&nbsp;&nbsp; CN341827
            <a href="#" id="loginOutOneScDirbook">安全退出</a>

        </span>
    </div>
    <div region="south" split="true" style="height: 30px; background: #D2E0F2; ">
        <div class="footer">版权所有 上海亿通国际股份有限公司 推荐使用1024×768或1280×768来浏览</div>
    </div>




        <div region="west" split="true" title="导航菜单" style="width:180px;" id="west">
            <div id="idaccordion">
                <!--  导航内容 -->
            </div>
        </div>


    <div id="mainPanle" region="center" style="background: #eee; overflow-y:hidden">
        <div id="tabs" class="easyui-tabs"  fit="true" border="false" >
            <div title="欢迎使用" style="padding:10px;overflow:hidden;" id="home" align="center">

                    <!-- 			 <div style="background:url(images/banner.jpg) no-repeat; width:777px; height:70px; margin:0 auto;"> -->
                    <div style="width:777px; height:70px; margin:0 auto;">
                        <div style="float:right;width:214px;margin-top:40px;line-height:20px;vertical-align:bottom;text-align:left;"><span id="suggest" onmouseover="this.className='navspan';" onmouseout="this.className=''" style="padding:0 5px 0 5px;">意见反馈</span>|<span id="help" style="padding:0 5px 0 5px;"><a href="#"><font color="#9E9E9E">帮助中心</font></a></span>|<span id="case" style="padding:0 5px 0 5px;"><a href="#"><font color="#9E9E9E">常用工具</font></a></span></div>
                    </div>
                    <!--  <h1> <span style="padding-left:10px; font-size: 16px; ">欢迎使用亿通订舱系统!</span></h1>-->
                    <iframe  scrolling="auto" frameborder="0"  src="cdus.html?method=searchList&className=com.easipass.ebw2.dao.model.Ebw2SysNoticeSet&forward=welcome" style="width:100%;height:85%;" align="top" ></iframe>

			</div>
		</div>
    </div>


    <!--修改密码窗口-->
    <div id="w" class="easyui-window" title="修改密码" collapsible="false" minimizable="false"
        maximizable="false" icon="icon-save"  style="width: 300px; height: 150px; padding: 5px;
        background: #fafafa;display:none;">
        <div class="easyui-layout" fit="true">
            <div region="center" border="false" style="padding: 10px; background: #fff; border: 1px solid #ccc;">
                <table cellpadding=3>
                    <tr>
                        <td>原始密码：</td>
                        <td><input id="txtOldPass" type="password" class="txt01" /></td>
                    </tr>
                    <tr>
                        <td>新密码：</td>
                        <td><input id="txtNewPass" type="password" class="txt01" /></td>
                    </tr>
                    <tr>
                        <td>确认密码：</td>
                        <td><input id="txtRePass" type="password" class="txt01" /></td>
                    </tr>
                </table>
            </div>
            <div region="south" border="false" style="text-align: right; height: 30px; line-height: 30px;">
                <a id="btnEp" class="easyui-linkbutton" icon="icon-ok" href="javascript:void(0)" >
                    确定</a> <a id="btnCancel" class="easyui-linkbutton" icon="icon-cancel" href="javascript:void(0)">取消</a>
            </div>
        </div>
    </div>
	<div id="kk" style="display: none">
	<div id="mm" class="easyui-menu" style="width:150px;">
		<div id="mm-tabclose">关闭</div>
		<div id="mm-tabcloseall">全部关闭</div>
		<div id="mm-tabcloseother">除此之外全部关闭</div>
		<div class="menu-sep"></div>
		<div id="mm-tabcloseright">当前页右侧全部关闭</div>
		<div id="mm-tabcloseleft">当前页左侧全部关闭</div>
		<div class="menu-sep"></div>
		<div id="mm-exit">退出</div>
	</div>
   </div>
<SCRIPT language=JavaScript>

if (screen.width == 1024 || screen.width ==1280)
{
document.write('您的显示器分辨率仅为' + screen.width + '×' + screen.height + ',推荐使用1024×768或1280×768来浏览');
}

</SCRIPT>
<div style="width: 1; height: 1; display:none;">
<!--<script type="text/javascript">var cnzz_protocol = (("https:" == document.location.protocol) ? " https://" : " http://");document.write(unescape("%3Cspan id='cnzz_stat_icon_3337966'%3E%3C/span%3E%3Cscript src='" + cnzz_protocol + "s21.cnzz.com/stat.php%3Fid%3D3337966' type='text/javascript'%3E%3C/script%3E"));</script>-->
<script>
$(function(){
			var ro = '';
			if(ro=='1')
				freezeAll();
		});

		function freezeAll(){
		$(":input").each(function(){
		if($(this).hasClass("chuansi_input")) return;
		if($(this).attr("type") == "button") $(this).hide();
		$(this).attr("readonly", true).css("color", "#666666");
		if(!$(this).hasClass("combo-f")) return;
		var self = $(this);
		$(this).combo({
			onShowPanel:function(){self.combo("hidePanel");}
		});
	});
		$(".datagrid-toolbar").hide();
		$("select").attr("disabled", true);
		deleteAttach = function(){};
		}
</script>
</div>







<html xmlns="http://www.w3.org/1999/xhtml">
<!DOCTYPE html>
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <script type="text/javascript" src="/epb/js/i18n/message_zh_CN.js"></script>
    <meta charset="UTF-8">
    <title>悬浮窗口</title>
    <style>
        .floating-window {
            position: fixed; /* 设置为fixed定位 */
            top: 60%;
            bottom: 30%;
            right: 50px;
            width: 50px;
            height: 50px; /* 设置高度 */
            background-color: #fff; /* 设置背景色 */
            z-index: 1000; /* 设置z-index值使其在最上层显示 */
            border-radius: 6px;
            box-shadow: 0 0 30px rgb(43, 58, 73);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: bold;
            color: #fff;
            user-select: none;
        }
        .myImage {
            width: 120px;
            height: 120px;
        }
        /* 关闭按钮的样式 */
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }
        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }
    </style>
</head>
<body>
<!-- 页面内容 -->

<div id="floating-window" class="floating-window">
    <span class="close" onclick="closeFloaty()">&times;</span>
    <p align="center" onclick="window.open('cdus.html?method=shippingTradeFin');"><image class="myImage" src="images/float.png"></image></p>
</div>

<script>
    function closeFloaty() {
        var floatyWindow = document.getElementById('floating-window');
        if (floatyWindow) {
            floatyWindow.style.display = 'none';
        }
    }
</script>
</body>
</html>





<html xmlns="http://www.w3.org/1999/xhtml">
<!DOCTYPE html>
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <script type="text/javascript" src="/epb/js/i18n/message_zh_CN.js"></script>
    <meta charset="UTF-8">
    <title>悬浮窗口</title>
    <style>
        .floating-window1 {
            position: fixed; /* 设置为fixed定位 */
            top: 45%;
            bottom: 30%;
            right: 50px;
            width: 50px;
            height: 50px; /* 设置高度 */
            background-color: #fff; /* 设置背景色 */
            z-index: 1000; /* 设置z-index值使其在最上层显示 */
            border-radius: 6px;
            box-shadow: 0 0 30px rgb(43, 58, 73);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: bold;
            color: #fff;
            user-select: none;
        }
        .myImage {
            width: 120px;
            height: 120px;
        }
        /* 关闭按钮的样式 */
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }
        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }
    </style>
</head>
<body>
<!-- 页面内容 -->

<div id="floating-window1" class="floating-window1">
    <span class="close" onclick="closeFloaty1()">&times;</span>
    <p align="center" onclick="window.open('cdus.html?method=shippingTradeFinAd');"><image class="myImage" src="images/float1.png"></image></p>
</div>

<script>
    function closeFloaty1() {
        var floatyWindow = document.getElementById('floating-window1');
        if (floatyWindow) {
            floatyWindow.style.display = 'none';
        }
    }
</script>
</body>
</html>
</body>
</html>