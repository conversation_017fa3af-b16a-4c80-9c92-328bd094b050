# -*- coding: utf-8 -*-
"""
鸭鸭出击：inttra  https://booking.inttra.com/create(域名已变更为https://booking.inttra.e2open.com)
http://ftp.hgj.net/ftp/%E6%B5%B7%E8%BF%90%E7%BB%84/%E8%AE%A2%E8%88%B1/%E8%BF%AD%E4%BB%A3%E5%86%85%E5%AE%B9/60%E9%B8%AD%E9%B8%AD%E5%87%BA%E5%87%BB--INTTRA/
"""
import re
import os
import time
import datetime
import json
from threading import Thread
from urllib.parse import urlparse

from lxml import etree
from selenium.webdriver import Chrome
from retry.api import retry
from requests.cookies import RequestsCookieJar
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.select import Select
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By

from spiders.base import Base
from utils.util_aes import aes_ecb_decrypt
from utils.util_exceptions import CurrencyException
from utils.util_normal import insert_enter_to_string
from utils.util_operate_mysql import MysqlSpiderUtil
from spiders.inttra_booking.config import namespace
from conf import CONTAINER_TYPE_HGJ_2_INTTRA, RECORD_STATE, SERVER_ENV, DONT_INSERT_ADDITIONAL_CHARGES_LIST, SPIDER_MYSQL_CONFIG, PROJECT_PATH, NAME


class InttraSpider(Base):
    task_name = "inttra"
    url = 'https://booking.inttra.e2open.com/create'

    # 船公司名称选项
    carrier_name_dict = {
        'CMA': 'CMA CGM',
        'CNC': 'CNC',
        'ANL': 'ANL CONTAINER LINE',
        "MSC": "MSC",
        "HLC": "HAPAG-LLOYD"
    }

    def __init__(self, msg, browser, service):
        Base.__init__(self)
        # 数据
        self.cur_msg = msg
        # 处理预配箱数据
        self.container_prefect_info = self.parse_container_info(self.cur_msg)
        self.browser: Chrome = browser
        self.service = service
        # 录屏开关
        self.screen_flag = True                                                 # 截屏
        self.is_need_screen = False                                             # 标记是否截屏
        self.util_mysql = MysqlSpiderUtil(**SPIDER_MYSQL_CONFIG)
        self.insert_id = None
        # recordId+:+account做的16位的md5为密钥对password做的解密
        self.account, self.password = self.cur_msg["accountBean"]["account"], aes_ecb_decrypt((self.cur_msg["recordId"], self.cur_msg["accountBean"]["account"]), self.cur_msg["accountBean"]["password"])
        self.cookies = RequestsCookieJar()

    def get_cookie(self):
        url = "https://api.inttra.e2open.com/auth"
        headers = {
            'host': 'api.inttra.e2open.com',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/107.0.1418.68',
            'content-type': 'application/json',
            'cookie': 'locale_pref=en; INTTRA_LANGUAGE_ID=1'
        }
        data = {
            "grant_type": "password",
            "username": self.account,
            "password": self.password
        }

        response = self.request(url, method="post", headers=headers, data=data, topic="json")
        cookie_params = response.json()
        verify_url = f"https://api.inttra.e2open.com/auth/login?authorization={cookie_params['access_token']}&syncdomain=true&redirectUrl="
        response = self.request(verify_url, allow_redirects=False)
        self.cookies = response.cookies

    def login(self):
        self.is_need_screen = True
        key = f"{self.task_name}-{self.account}-{self.password}"
        for _ in range(1, 5):
            self.browser.delete_all_cookies()
            try:
                # 如果已经登陆过就使用登陆过的cookie，没有的话使用接口登陆，其他情况保留自动化登陆方式
                cookies = namespace.get(key, [])
                if cookies:
                    if self.browser.get_cookies(): raise ValueError("cookie未删除")
                else:
                    try:
                        self.get_cookie()
                        cookies = [vars(one) for one in self.cookies]
                        for cookie in cookies:
                            if cookie["name"].upper() == "SESSION_KEY":
                                (token_cookie := cookie.copy()).update({"name": "TOKEN"})
                                cookies.append(token_cookie)
                    except Exception as e:
                        self.logger.error(e)

                # 如果当前页面是create页面，添加cookie
                self.url == self.browser.current_url and [self.browser.add_cookie({"name": one["name"], "value": one["value"], "domain": ".inttra.e2open.com"}) for one in cookies]
                self.browser.get(self.url)
                try:
                    WebDriverWait(self.browser, 5, 0.3).until(lambda d: d.find_element(By.ID, "contractNumber"))
                    namespace[key] = self.browser.get_cookies()
                    return True
                except:
                    pass
                WebDriverWait(self.browser, 20, 0.5).until(EC.element_to_be_clickable((By.ID, 'email'))).send_keys(self.account)
                self.clear_and_send_keys(self.browser, 'id', 'password', self.password + Keys.ENTER)
                WebDriverWait(self.browser, 20, 0.5).until(lambda x: urlparse(self.url).hostname in x.current_url)
                namespace[key] = self.browser.get_cookies()
                self.browser.get(self.url)  # 解决有时会跳转到search页面的问题
                WebDriverWait(self.browser, 30, 0.3).until(lambda d: d.find_element(By.ID, "contractNumber"))
                return True
            except Exception as e:
                self.logger.error(e, exc_info=True)
                self.error_info = "未登录成功！"
        else:
            return False

    def upload_task(self):
        """上传"""
        try:
            task = self.cur_msg['detail']["bookingOrderInfoBean"]
            self.logger.info("登陆完成")
            # part 1 General Details part 2 Parties part 4 Transport   part 7 Payment Details
            if self.general_details(task) is False or self.parties(task) is False or self.transport(task) is False or self.payment_details(task) is False:
                return False
            self.logger.info("1、2、4、7完成")
            # part 3 References
            self.references()
            self.logger.info("3完成")
            # part 5 Cargo
            self.cargo(task)
            self.logger.info("5完成")
            # part 6 Container
            self.container(task)
            self.logger.info("6完成")
            # part 8 Comments & Notifications
            self.comments_notifications(task)
            self.logger.info("8完成")
            # 填写船公司，船公司的加载较慢先填写其他选项，最后再填入
            self.select_carrier()
            # 检查
            if not self.check_port_of_loading(task):
                return False
            self.logger.info("船公司选择完成")

            # submit之前先截图
            delegation_no = self.cur_msg['detail']['delegationNo']
            self.screen_picture(delegation_no, self.browser)

            # 提交
            if SERVER_ENV in ['dev', 'beta', 'local', "uat"]:
                self.cur_bill_no = str(int(time.time()))
            else:
                # 设置重试标记
                self.is_not_need_retry = True
                self.browser.find_element(By.XPATH, '//*[@id="create-booking-container"]/div/div[2]/div/div[2]/button').click()
                for _ in range(3):
                    time.sleep(self.long_time)
                    added_window = self.browser.find_elements(By.XPATH, "//a[text()='No thanks, Continue booking without service']")
                    added_window and added_window[0].is_displayed() and added_window[0].click()
                time.sleep(10)
                try:
                    self.cur_bill_no = self.query_delegation_no()
                    self.logger.info("提交完成")
                except Exception as e:
                    self.error_info = "提单号获取失败,请查询！"
                    self.logger.error(e, exc_info=True)
                if not self.cur_bill_no:
                    return False
        except Exception as e:
            self.logger.error(e, exc_info=True)
            self.error_info = self.error_info if self.error_info else "订舱失败！"
            return False
        return True

    def check_port_of_loading(self, task):
        """判断起运港的值与当前页面的起运港值是否一致"""
        place_of_loading_code = task['sailingScheduleInfoBean']['portOfLoadingCode']
        port_of_loading_element = self.browser.find_element('id', 'portOfLoad0')
        current_port_of_loading_value = port_of_loading_element.get_attribute('value')

        if place_of_loading_code not in current_port_of_loading_value:
            self.error_info = f"起运港值不匹配，当前值：{current_port_of_loading_value}，应为：{place_of_loading_code}"
            self.logger.info(f"起货港值不匹配，当前值：{current_port_of_loading_value}，应为：{place_of_loading_code}")
            return False
        else:
            return True

    def general_details(self, task: dict):
        """1 general_details"""
        detail_dict = task['bookingBaseInfoBean']
        contract_no = detail_dict['contractNo']  # 合约号
        booking_office_port_code = self.cur_msg['bookingExtendData']['bookingOfficePortCode']         # 装货港五码

        # Contract Number 输入约号
        self.clear_and_send_keys(self.browser, 'id', 'contractNumber', contract_no)

        # Booking Office 选择装货港
        if self.send_port_code_to_input_table('id', 'bookingOffice', booking_office_port_code) is False:
            error_info = f'Booking Office {booking_office_port_code}对照失败，重启浏览器'
            self.logger.error(error_info)
            raise CurrencyException(error_info)

        return True

    def parties(self, task: dict):
        """2 parties"""

        # 检查是否有弹窗
        html = self.browser.page_source
        ele = etree.HTML(html)
        if ele.xpath("/html/body/div[2]/div[2]/p/span[contains(text(), 'While we may have a brand new logo')]"):
            self.browser.find_element(By.XPATH, "/html/body/div[2]/button[1]").click()

        # 点击下拉其他补充的按钮
        self.browser.find_element(By.XPATH, '//h6/a/span[contains(text(), "Additional Parties")]').click()

        # 格式化收发通信息
        relation_dict = self.parse_relation_info(task)

        enterprise_id = self.cur_msg['detail']["enterpriseId"]
        carrier_code = self.cur_msg["detail"]['carrierCode']
        time.sleep(self.long_time)

        # 当enterpriseId为1867089026105733121，船公司为cma，设置forwarder为：SHANGHAI PANMAX INT'L LOGISTICS CO.,LTD
        if carrier_code.lower() == "cma" and enterprise_id == "1867089026105733121":
            # 点击Lookup按钮
            self.browser.find_element(By.XPATH, "//div[@id='forwarder']//span[@class='create_lookup']").click()
            # 等待元素出现
            WebDriverWait(self.browser, 10,).until(EC.presence_of_element_located(
                (By.XPATH, "//div[@id='forwarder']//div[@placement='bottom-right']//*[@id='assign_shipper1']")))
            # 点击My Partners选项
            self.browser.find_element(By.XPATH, "//div[@id='forwarder']//div[@placement='bottom-right']//*[@id='assign_shipper1']").click()
            # 等待元素出现
            WebDriverWait(self.browser, 10).until(EC.presence_of_element_located(
                (By.XPATH, "//table[@class='table dataTable table-bordered table-hover']/tbody/tr/td[1]")))
            # 点击 table 中的第一个 td 元素
            value = self.browser.find_element(By.XPATH, "//table[@class='table dataTable table-bordered table-hover']/tbody/tr/td[1]").text
            if value == "SHANGHAI PANMAX INT'L LOGISTICS CO.,LTD":
                self.browser.find_element(By.XPATH, "//table[@class='table dataTable table-bordered table-hover']/tbody/tr/td[1]").click()
            else:
                self.error_info = "forwarder选择失败！"
                return False

        for notify_type, adict in relation_dict.items():
            head_str = insert_enter_to_string(adict['name'], rows_count=2)
            addr = adict['address'].replace('\t', '')
            addr = insert_enter_to_string(addr, rows_count=5)

            if notify_type == 'shipper':
                # Shipper 输入发货人抬头，等待点击 add party details，输入发货人地址
                self.clear_and_send_keys(self.browser, 'id', 'shipperPartyBox', head_str)
                # 添加
                for i in range(2):
                    self.browser.find_element(By.XPATH, '//div[@id="shipper"]//button[@id="address-contact-button"]').click()
                    try:
                        self.browser.find_element(By.XPATH, '/html/body/ngb-modal-window/div/div/div[3]/button').click()
                    except:
                        break
            elif notify_type == 'consignee':
                # Consignee 收货人名称/地址
                self.clear_and_send_keys(self.browser, 'id', 'consigneePartyBox', head_str)
                # 添加
                for i in range(2):
                    self.browser.find_element(By.XPATH, '//div[@id="consignee"]//button[@id="address-contact-button"]').click()
                    try:
                        self.browser.find_element(By.XPATH, '/html/body/ngb-modal-window/div/div/div[3]/button').click()
                    except:
                        break
            elif notify_type == 'notify':
                # Notify Party 通知人名称/地址
                try:
                    self.clear_and_send_keys(self.browser, 'id', 'notifyPartyPartyBox', head_str)
                except:
                    self.browser.find_element(By.XPATH, '//*[@id="refToggle"]/a').click()
                    self.clear_and_send_keys(self.browser, 'id', 'notifyPartyPartyBox', head_str)
                # 点击添加
                for i in range(2):
                    self.browser.find_element(By.XPATH, '//div[@id="notifyParty"]//button[@id="address-contact-button"]').click()
                    try:
                        self.browser.find_element(By.XPATH, '/html/body/ngb-modal-window/div/div/div[3]/button').click()
                    except:
                        break
            else:
                # 第二通知人名称/地址
                if head_str:
                    try:
                        self.clear_and_send_keys(self.browser, 'id', 'additionalNotifyParty1PartyBox', head_str)
                    except:
                        self.browser.find_element(By.XPATH, '//*[@id="refToggle"]/a').click()
                        self.clear_and_send_keys(self.browser, 'id', 'additionalNotifyParty1PartyBox', head_str)

                    # 点击添加
                    for i in range(2):
                        self.browser.find_element(By.XPATH, '//div[@id="additionalNotifyParty1"]//button[@id="address-contact-button"]').click()
                        try:
                            self.browser.find_element(By.XPATH, '/html/body/ngb-modal-window/div/div/div[3]/button').click()
                        except:
                            break

            if head_str:
                # 输入地址
                if addr.count('\n') < 4:
                    self.clear_and_send_keys(self.browser, 'id', 'partAddress', addr + Keys.ENTER)
                else:
                    self.clear_and_send_keys(self.browser, 'id', 'partAddress', addr)
                time.sleep(self.mid_time)
                self.deal_alert_window()
                self.browser.find_element(By.ID, 'savePartyAddressContactButton').click()
                time.sleep(self.mid_time)
                retry_click_flag = self.deal_alert_window()

                # 保存
                if retry_click_flag:
                    self.browser.find_element(By.ID, 'savePartyAddressContactButton').click()
        return True

    def references(self):
        """3 全部输入海管家委托编号"""
        delegation_no = self.cur_msg['detail']['delegationNo']  # 委托编号
        self.clear_and_send_keys(self.browser, 'id', 'shippersRef', delegation_no + Keys.TAB + delegation_no + Keys.TAB + delegation_no)
        return True

    def transport(self, task: dict):
        """4"""
        place_of_carrier_receipt = task['sailingScheduleInfoBean']['placeOfReceiptCode']        # 收货地五码
        place_of_loading_code = task['sailingScheduleInfoBean']['portOfLoadingCode']            # 装货港五码
        destination_code = task['sailingScheduleInfoBean']['destinationCode']                   # 目的地五码
        place_of_delivery_code = task['sailingScheduleInfoBean']['placeOfDeliveryCode']         # 交货地五码
        port_of_discharge_code = task['sailingScheduleInfoBean']['portOfDischargeCode']         # 卸货港五码
        etd = task['sailingScheduleInfoBean']['etd'].split(' ')[0]                              # 开航日
        vessel = task['sailingScheduleInfoBean']['vessel']                                      # 船名
        voyage_no = task['sailingScheduleInfoBean']['voyageNo']                                 # 航次
        transport_clause = task['bookingBaseInfoBean']['transportClause']                       # 运输条款 CY-CY

        # Move Type
        if transport_clause in ['CY-CY', 'CY-RAMP']:
            s_visible_text = 'Port, Ramp, or CY to Port, Ramp, or CY'
        elif transport_clause in ['DOOR-CY']:
            s_visible_text = 'Door to Port, Ramp, or CY'
        elif transport_clause in ['DOOR-DOOR']:
            s_visible_text = 'Door to Door'
        elif transport_clause in ['CY-DOOR']:
            s_visible_text = 'Port, Ramp, or CY to Door'
        else:
            self.error_info = f'{transport_clause} 协议匹配错误'
            return False

        if transport_clause not in ['CY-CY', 'CY-RAMP']:
            for _ in range(3):
                try:
                    # 选择两次，有时选一次并不会生效
                    Select(self.browser.find_element(By.ID, 'moveType')).select_by_visible_text(s_visible_text)
                    time.sleep(self.long_time)
                    Select(self.browser.find_element(By.ID, 'moveType')).select_by_visible_text(s_visible_text)
                    break
                except:
                    # 确保选择框是可以点击的
                    self.browser.find_element(By.ID, 'moveType').click()
                    time.sleep(self.mid_time)
                    self.browser.find_element(By.ID, 'moveType').click()
            else:
                self.error_info = f'选择Move Type失败，运输协议：{transport_clause}'
                return False

        # Place of Carrier Receipt 承运人接货地
        if self.send_port_code_to_input_table(by='id', rule='placeOfCarrierReceipt', port_code=place_of_carrier_receipt) is False:
            self.error_info = f'Place of Carrier Receipt 五码：{place_of_carrier_receipt} 匹配失败'
            return False

        # Place of Carrier Delivery 承运人交货地 目的地或交货地五码
        # code = destination_code if destination_code != '' else place_of_delivery_code
        code = place_of_delivery_code
        if self.send_port_code_to_input_table(by='id', rule='placeOfCarrierDelivery', port_code=code) is False:
            self.error_info = f'Place of Carrier Delivery 五码：{code} 匹配失败'
            return False

        # Main Carriage
        # Port of Load 装货港
        if self.send_port_code_to_input_table(by='id', rule='portOfLoad0', port_code=place_of_loading_code) is False:
            self.error_info = f'Port of Load {place_of_loading_code} 对照失败'
            return False

        # Port of Discharge 卸货港 输入卸货港五码
        if self.send_port_code_to_input_table(by='id', rule='portOfDischarge0', port_code=port_of_discharge_code) is False:
            self.error_info = f'运输协议：{transport_clause} 卸货港: {port_of_discharge_code} 匹配失败'
            return False

        # ETD dd-mmm-yyyy.
        etd = datetime.datetime.strptime(etd, '%Y-%m-%d').strftime('%d-%b-%Y')
        self.clear_and_send_keys(self.browser, 'id', 'etd0', etd)
        # Vessel + Voyage
        self.clear_and_send_keys(self.browser, 'id', 'vessel0', vessel + Keys.TAB + voyage_no)
        return True

    def cargo(self, task: dict):
        """5"""
        cargo_info_list = task['cargoInfoBeanList']
        cargo_dict = cargo_info_list[0]
        hsCode = cargo_dict['hsCode']  # 只取货物信息里的第一个
        englishProductName = cargo_dict['englishProductName']  # 英文品名
        total_grossWeight = str(round(sum([float(cargo_dict['grossWeight']) for cargo_dict in cargo_info_list]), 4))  # 毛重
        total_volume = str(round(sum([cargo_dict['volume'] for cargo_dict in cargo_info_list]), 4))  # 体积

        self.clear_and_send_keys(self.browser, 'id', 'hsCode0', hsCode)
        self.clear_and_send_keys(self.browser, 'id', 'cargoDescription0', englishProductName.replace('\t', ''))
        self.clear_and_send_keys(self.browser, 'id', 'cargoWeight0', total_grossWeight)
        self.clear_and_send_keys(self.browser, 'id', 'grossVolume0', total_volume)
        return True

    def container(self, task: dict):
        """6"""
        container_infos = self.container_prefect_info
        transport_clause = task['bookingBaseInfoBean']['transportClause']  # 运输条款 CY-CY
        # 格式化收发通信息
        relation_dict = self.parse_relation_info(task)
        mailing_dict = relation_dict["consignee"]  # 收货人信息

        for index, container_info in enumerate(container_infos):
            if index > 0:
                # 点击新增
                self.click_by_js(self.browser, 'xpath', f'//app-containers//button[@id="addCharge{index - 1}"]')
                time.sleep(self.short_time)

            container_number = str(container_info['containerNumber'])  # 箱量
            container_type = container_info['containerType']  # 箱型code

            self.clear_and_send_keys(self.browser, 'id', f'quantity{index + 1}', container_number + Keys.TAB + CONTAINER_TYPE_HGJ_2_INTTRA[container_type])
            time.sleep(self.long_time)
            try:
                # 点击no thanks
                self.click_by_js(self.browser, 'id', '16bdf4c1-3d38-47ec-d30a-785196a0a0aa')
            except:
                pass
            WebDriverWait(self.browser, 20, 0.5).until(EC.element_to_be_clickable((By.XPATH, '//ngb-typeahead-window'))).click()
            time.sleep(self.mid_time)

            if container_info['soc']:
                self.click_by_js(self.browser, 'id', f'cont-owned{index}')

            if container_type in ['40NOR', '45NOR']:
                # 当箱型为40 Reefer High Cube (45R1)----对应海管家40NOR和45NOR 时，请务必点击 add reefer settings 按钮，同时选择Non-active reefer
                self.click_by_js(self.browser, 'xpath', f'//app-containers//app-reefer-btn/button')
                time.sleep(self.mid_time)
                try:
                    self.click_by_js(self.browser, 'id', '16bdf4c1-3d38-47ec-d30a-785196a0a0aa')
                except:
                    pass
                self.click_by_js(self.browser, "xpath", "//form[@id='reeferSettingsModalForm']//button[text()=' Non-Operating Reefer']")
                self.click_by_js(self.browser, 'id', 'saveReeferButton')

            if transport_clause in ['CY-DOOR']:
                # 点击 add Haulage Details
                self.browser.find_element(By.XPATH, f'//*[@id="create-booking-container"]/div/app-containers/form/div/div[{index + 1}]//app-haulage-btn/button').click()
                time.sleep(self.mid_time)
                # company name
                self.clear_and_send_keys(self.browser, 'id', 'shipToCompanyName', mailing_dict['name'])
                self.browser.find_element(By.ID, 'shipToAddress').click()
                time.sleep(self.mid_time)
                # 处理弹窗
                self.deal_alert_window()
                # address
                self.clear_and_send_keys(self.browser, 'id', 'shipToAddress', mailing_dict['address'].replace('\t', ''))
                self.browser.find_element(By.ID, 'haulageSave').click()
                time.sleep(self.mid_time)
                retry_click_flag = self.deal_alert_window()
                # 保存
                if retry_click_flag:
                    self.browser.find_element(By.ID, 'haulageSave').click()
        return True

    def deal_alert_window(self):
        """处理弹窗"""
        retry_click_flag = False  # 标记是否需要再次点击保存
        for i in range(2):
            try:
                self.browser.find_element(By.XPATH, '/html/body/ngb-modal-window[2]/div/div/div[3]/button').click()
                retry_click_flag = True
            except:
                pass
            try:
                self.browser.find_element(By.XPATH, '/html/body/ngb-modal-window[3]/div/div/div[3]/button').click()
                retry_click_flag = True
            except:
                pass
        return retry_click_flag

    def payment_details(self, task: dict):
        """7"""
        place_of_loading_code = task['sailingScheduleInfoBean']['portOfLoadingCode']
        #                  起运港费用（local charge）      海运费             附加费
        visible_text_list = ['Origin Port Charges', 'Basic Freight', 'Additional Charges']
        for i in range(0, 3):
            if i == 2 and self.cur_msg["detail"]["carrierCode"].upper() in DONT_INSERT_ADDITIONAL_CHARGES_LIST:
                continue
            if i != 0:
                self.click_by_js(self.browser, 'xpath', f'//app-payment-details//button[@id="addCharge{i - 1}"]')
            # 等待选择框加载
            WebDriverWait(self.browser, 30, 0.5).until(EC.presence_of_element_located((By.ID, f'selectChargeType{i}')))
            time.sleep(self.long_time)

            # 第二行请选择 basic freight，第三行选 Additional Charges    payment请根据海管家的【付款方式】来判断，
            Select(self.browser.find_element(By.ID, f'selectChargeType{i}')).select_by_visible_text(visible_text_list[i])

            paymentMethod = task['bookingBaseInfoBean']['paymentMethod']
            if i == 0:
                # 装货港
                Select(self.browser.find_element(By.ID, f'selectFreightTerm{i}')).select_by_visible_text('Pre-paid')
                Select(self.browser.find_element(By.ID, f'selectPayer{i}')).select_by_visible_text('Booker')
                port_code = place_of_loading_code
            elif paymentMethod == 'FP':
                # 预付  选装货港
                Select(self.browser.find_element(By.ID, f'selectFreightTerm{i}')).select_by_visible_text('Pre-paid')
                Select(self.browser.find_element(By.ID, f'selectPayer{i}')).select_by_visible_text('Booker')
                port_code = place_of_loading_code
            elif paymentMethod == 'FC':
                # 到付 location输入卸货港五码
                Select(self.browser.find_element(By.ID, f'selectFreightTerm{i}')).select_by_visible_text('Collect')
                Select(self.browser.find_element(By.ID, f'selectPayer{i}')).select_by_visible_text('Consignee')
                port_code = task['sailingScheduleInfoBean']['portOfDischargeCode']  # 卸货港五码
            else:
                # 第三方  location 输入付款地
                port_code = task['bookingBaseInfoBean']['paymentPlaceCode']      # 付款地code
                Select(self.browser.find_element(By.ID, f'selectFreightTerm{i}')).select_by_visible_text('Payable Elsewhere')
                Select(self.browser.find_element(By.ID, f'selectPayer{i}')).select_by_visible_text('Consignee')

            # 输入location
            if self.send_port_code_to_input_table(by='id', rule=f'paymentLocation{i}', port_code=port_code) is False:
                self.error_info = f'for Payment Details -- Payment Location {i + 1} {port_code} 港口不存在'
                return False
            time.sleep(self.mid_time)
        return True

    def comments_notifications(self, task: dict):
        """8"""
        bookingRemark = task['bookingBaseInfoBean']['bookingRemark']  # 备注
        lines = re.sub(r'[\u4e00-\u9fa5]|\t', '', bookingRemark)

        try:
            self.click_by_js(self.browser, 'id', '16bdf4c1-3d38-47ec-d30a-785196a0a0aa')
        except:
            pass
        self.clear_and_send_keys(self.browser, 'xpath', '//textarea[@class="form-control form-control-sm comments-and-notification ng-untouched ng-pristine ng-valid"]', lines + Keys.TAB + '<EMAIL>')
        return True

    def select_carrier(self):
        """填写船公司"""
        carrier_code = self.cur_msg["detail"]['carrierCode']  # 船公司

        # 选择船公司
        try:
            ele = etree.HTML(self.browser.page_source)
            carriers = ele.xpath("//select[@id='inputCarrier']/option/text()")
            carriers = list(map(lambda x: x.strip(), carriers))
            self.logger.info(str(carriers))
            index = carriers.index(self.carrier_name_dict[carrier_code])
            s = Select(self.browser.find_element(By.ID, 'inputCarrier'))
            s.select_by_index(index)
            time.sleep(self.long_time)
        except Exception as e:
            self.error_info = "船公司选择失败！"
            self.logger.error(e, exc_info=True)

    def send_port_code_to_input_table(self, by, rule, port_code):
        for _ in range(3):
            self.clear_and_send_keys(self.browser, by, rule, port_code)
            if self.wait_fro_window():
                res = self.chose_one_row(port_code=port_code)
                if res == -1:
                    # 不需要再尝试
                    return False
                elif res is True:
                    return res
        else:
            return False

    def wait_fro_window(self):
        """等待加载弹窗"""
        try:
            WebDriverWait(self.browser, 40, 0.5).until(EC.element_to_be_clickable((By.XPATH, '//ngb-typeahead-window/button')))
            return True
        except:
            return False

    def chose_one_row(self, port_code):
        """在弹出的信息中，选择对应的一行"""
        for i in range(2):
            eles = self.browser.find_elements(By.XPATH, '//ngb-typeahead-window/button')
            for ele in eles:
                if ele.text == 'No matches found':
                    # 不需要再尝试
                    return -1
                elif self.judge_port_code(port_code, ele.text):
                    self.browser.execute_script("arguments[0].click();", ele)
                    try:
                        self.click_by_js(self.browser, 'id', '16bdf4c1-3d38-47ec-d30a-785196a0a0aa')
                    except:
                        pass
                    return True
            else:
                return False

    @staticmethod
    def judge_port_code(code, astr: str):
        """获取弹出的信息，并判断是否与五码匹配"""
        res = re.match('.*\((.*)\)$', astr).group(1)
        return res == code

    @retry(tries=3)
    def query_delegation_no(self):
        """查询委托编号"""
        api = "https://api.inttra.e2open.com/booking/search/simple"
        delegation_no = self.cur_msg['detail']['delegationNo']  # 委托编号

        for _ in range(3):
            # 获取 authorization
            authorization = self.browser.get_cookie("TOKEN").get("value", "")
            headers = {
                "authorization": authorization,
                "origin": "https://booking.inttra.e2open.com",
                "referer": "https://booking.inttra.e2open.com/",
                "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Safari/537.36",
            }
            params = {
                "from": 0,
                "query": delegation_no,
                "size": 50,
                "sort": [{"field": "lastModifiedDateUtc", "sortOrder": "desc"}]
            }
            try:
                res = self.request(method="post", url=api, headers=headers, data=params, topic="json", timeout=15)
            except:
                self.error_info = "提单号获取失败,请查询！"
            else:
                if res.status_code == 200:
                    self.error_info = ""
                    return self.parse_query_res(res.content.decode())
                else:
                    self.logger.error(f"提单号获取失败，刷新重试，当前状态码：{res.status_code}")
                    continue
        else:
            self.error_info = "提单号获取失败,请查询！"
            return False

    def parse_query_res(self, result_json):
        """解析查询结果并返回提单号"""
        result_dict = json.loads(result_json)
        delegation_no = self.cur_msg['detail']['delegationNo']  # 委托编号
        if result_dict['total'] == 0:
            # 上传失败，未查询到
            self.error_info = "未查询到记录，请核实"
            return False

        for result in result_dict['results']:
            if result['purchaseOrderNumbers'][0] == delegation_no:
                return result['inttraReferenceNumber']
        else:
            # 没有获取到
            self.error_info = "未查询到记录，请核实"
            return False

    def insert_sql_data(self):
        """持久化数据"""
        try:
            sql = "INSERT INTO inttra_booking_info (carrierCode, delegationNo, data, bookingStartTime) VALUES (%s, %s, %s, %s)"
            insert_info = self.util_mysql.update(sql, params=(self.cur_msg['detail']['carrierCode'], self.cur_msg['detail']['delegationNo'], json.dumps(self.cur_msg), time.strftime("%Y-%m-%d %H:%M:%S")))
            return insert_info[-1]
        except Exception as e:
            self.logger.error(e)

    def save_booking_result(self, message):
        try:
            sql = "UPDATE inttra_booking_info set mediaDownloadLink=%s, mediaPreviewLink=%s, bookingEndTime=%s, message=%s where id=%s"
            self.util_mysql.update(sql, params=(message["videoDownloadUrl"], message["videoPreviewUrl"], time.strftime("%Y-%m-%d %H:%M:%S"), json.dumps(message, ensure_ascii=False), self.insert_id))
        except Exception as e:
            self.logger.error(e, exc_info=True)

    def screen_picture(self, delegation_no, browser: Chrome):
        try:
            screenshot_png_path = os.path.join(PROJECT_PATH, 'docs', "images")
            if not os.path.exists(screenshot_png_path): os.mkdir(screenshot_png_path)
            image_name = f'{self.task_name}_{delegation_no}.png'
            file_path = os.path.join(screenshot_png_path, image_name)
            content_ele_list = browser.find_elements(By.XPATH, "//app-root/div")
            height = content_ele_list[0].size['height'] + 80 if content_ele_list else 1120
            self.browser.set_window_size(width=1792, height=height)
            browser.find_element(By.XPATH, "//app-root/div").screenshot(file_path)
        except Exception as e:
            self.logger.info(e, exc_info=True)
        finally:
            self.browser.set_window_size(width=1792, height=1120)

    def main(self):
        task = self.cur_msg['detail']["bookingOrderInfoBean"]
        booking_id = self.cur_msg["detail"]['bookingId']                    # 订舱ID
        carrier_code = self.cur_msg['detail']['carrierCode']                # 船公司
        delegation_no = self.cur_msg['detail']['delegationNo']              # 委托编号
        vessel = task['sailingScheduleInfoBean']['vessel']                  # 船名
        voyage_no = task['sailingScheduleInfoBean']['voyageNo']             # 航次
        transport_clause = task['bookingBaseInfoBean']['transportClause']   # 运输条款

        upload_flag = True

        self.logger.info(f"booking_id: {booking_id} 委托编号: {delegation_no} 开始上传")
        self.insert_id = self.insert_sql_data()
        # 提示不支持的条款
        if transport_clause not in ['CY-CY', 'CY-RAMP', 'CY-DOOR']:
            self.error_info = f'inttra 暂不支持运输条款：{transport_clause}'
            upload_flag = False

        if carrier_code not in self.carrier_name_dict.keys():
            self.error_info = f'inttra 不支持{carrier_code}'
            upload_flag = False
        elif vessel == '' or voyage_no == '':
            # 判断船名航次
            self.error_info = f'船名或航次不能为空'
            upload_flag = False
        else:
            container_infos = self.container_prefect_info
            for container_info in container_infos:
                container_type = container_info["containerSize"]+container_info["containerName"]  # 箱型
                if container_type not in CONTAINER_TYPE_HGJ_2_INTTRA.keys():
                    self.error_info = f'箱型错误 {container_type}'
                    upload_flag = False
                    break

        # 开始上传
        RECORD_STATE and Thread(target=self.screen_browser, args=(self.browser, delegation_no,)).start()

        if upload_flag and self.login():
            upload_flag = self.upload_task()
        else:
            upload_flag = False
        # 关闭截屏
        self.screen_flag = False
        # 获取提单号
        self.logger.info(f"booking_id: {booking_id} 委托编号: {delegation_no} 提单号：{self.cur_bill_no} 上传完成")
        self.error_info = "" if upload_flag else self.error_info
        if self.error_info:
            super().screen_picture(delegation_no, self.browser)
        message = self.send_result(msg=self.cur_msg, tag=1, flag=upload_flag, bill_no=self.cur_bill_no, error_info=self.error_info, search_result=self.search_result_str, is_have_video=True)
        self.save_booking_result(message)

    def __del__(self):
        self.screen_flag = False
        # 删除图片截图
        delegation_no = self.cur_msg['detail']['delegationNo']  # 委托编号
        self.remove_useless_file(delegation_no=delegation_no)


