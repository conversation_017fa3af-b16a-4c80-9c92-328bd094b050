# -*- coding: utf-8 -*-
"""
鸭鸭的望远镜
2012444899
https://www.cma-cgm.com.cn/LaraToolkit/BookingInquery
原型：http://ftp.hgj.net/ftp/%E6%B5%B7%E8%BF%90%E7%BB%84/%E8%AE%A2%E8%88%B1/%E8%BF%AD%E4%BB%A3%E5%86%85%E5%AE%B9/64%E9%B8%AD%E9%B8%AD%E6%9C%9B%E8%BF%9C%E9%95%9C/
"""
import re
import json
import time
import random
import datetime
from queue import Queue
from threading import Thread

from hgj_loguru import hgj_logger as logger

from spiders.base import Base
from conf import SERVER_ENV, HGJ_BOOKING_ORIGIN_BILL_NO_API
from utils import create_authorization
from utils.util_send_cancel_info import send_error
from utils.util_proxy import get_proxy
from utils.util_normal import send_message_to_developer, remove_html

# 队列存放的个数
MAX_SIZE = 5
# 设置消费线程的个数
CONSUMER_THREAD_NUM = 3


class Telescope(Base):
    task_name = "Telescope"

    def __init__(self):
        super(Telescope, self).__init__()
        self.post_api_cma = "https://www.cma-cgm.com.cn/LaraToolkit/BookingInquery/BookingInqueryData"
        self.queue = Queue(maxsize=MAX_SIZE)
        self.success_msg_list = ['以下爬取成功，已补录单号：\n', ]
        self.false_msg_list = ['以下爬取失败，请核查：\n', ]
        self.email_to = ['<EMAIL>'] if SERVER_ENV == 'prod' else ['<EMAIL>']

    def handle_bill_no(self):
        """处理提单数据"""
        while True:
            one = self.queue.get()
            if not one and self.queue.qsize() >= CONSUMER_THREAD_NUM:
                self.queue.put(one)
                continue
            if not one and self.queue.qsize() < CONSUMER_THREAD_NUM:
                break
            self.logger.info(f"取出队列提单号信息: {one}")
            try:
                if not one["billNo"]: continue
                search_result, title = self.get_second_bill_no(one)
                self.deal_res(one, search_result)
                # 发送取消提单
                data = {"carrier": one["carrierCode"], "billNo": search_result["second_bill_no"] if search_result.get("second_bill_no") else one["billNo"],
                        "delegationNo": one["delegationNo"], "vessel": one["vessel"], "voyageNo": one["voyageNo"]}
                title and send_error(data, title, is_need_wait=True)
            except Exception as e:
                self.logger.error(e, exc_info=True)
            time.sleep(random.randint(1, 2))

    def get_tasks(self):
        """从数据库查询任务"""
        data = ["CMA", "CNC", "ANL"]
        headers = {"authorization": create_authorization("booking")}
        try:
            response = self.request(url=HGJ_BOOKING_ORIGIN_BILL_NO_API, method="post", data=data, headers=headers, topic="json")
            if not response:
                raise KeyError("调用API接口失败!!!")
            response_json = response.json()
            data = response_json["data"]
        except Exception as e:
            message = "".join([str(__file__).rsplit("/", 1)[-1], ":", str(e)])
            send_message_to_developer(message, [])
            logger.error(f"请求接口获取数据失败: {e}", exc_info=True)
        else:
            for one in data:
                self.queue.put(one)
                logger.info(f"放入队列提单号信息: {one}")
            for i in range(CONSUMER_THREAD_NUM):
                self.queue.put(None)

    def get_second_bill_no(self, origin_data):
        """获取提单号"""
        payload = {'BookingNo': '',
                   'CusRef': origin_data["billNo"]}
        res_dict = {}
        for i in range(3):
            try:
                response = self.request(method="post", url=self.post_api_cma, data=payload, topic="form", timeout=20, proxy=get_proxy("pz"))
                if response and response.status_code >= 500 or (('查询失败!请联系你的信息管理员!' or "查询请求超时，请稍后重试") in response.content.decode()):
                    continue
                res_dict = self.parse_response(origin_data, response)
                break
            except:
                pass
        return res_dict

    def parse_response(self, origin_data, response):
        """解析查询结果"""
        res_dict = {"code": 400}
        resp_dict = json.loads(response.content.decode())
        data_str = resp_dict['data']
        info_title = re.match(r'<p style="font-weight: bold; font-size: large">(.*?)</p>.*', data_str, re.S).group(1)
        bill_no = re.match(r'.*?<br/>订舱单号: (.*?)<br/>.*', data_str, re.S)

        if bill_no:
            # 查询到了结果
            bill_no = bill_no.group(1)
            # 判断是否是提单号，用获取到的结果前两位是否是英文字母来判断是否是提单号
            if bill_no[:2].isalpha():
                res_dict['code'] = 200
                res_dict['second_bill_no'] = bill_no
                res_dict['title'] = info_title.strip('<br/>')
            else:
                res_dict['msg'] = f'{origin_data["billNo"]} - 查询结果：{bill_no} - 标题：{info_title}'
        elif info_title in ['查询失败!请联系你的信息管理员!', '请求过于频繁', '查询请求超时，请稍后重试。']:
            # 再次获取信息
            res_dict['msg'] = f'{origin_data[1]} - 标题：{info_title}'
        elif '没有找到该订舱数据:请输入正确的' in info_title or '请输入订舱单号或者客户订舱号' in info_title:
            # 提单号错误
            res_dict['msg'] = f'{origin_data["billNo"]} - 标题：{info_title}'
        else:
            # 报错，标记并推送
            msg = f'消息处理失败，请尽快优化。\n{remove_html(data_str)}'
            self.logger.warning(msg)
            send_message_to_developer(msg, [])
            res_dict['msg'] = f'{origin_data["billNo"]} - 消息解析失败，请优化解析规则'
        return res_dict, info_title.strip('<br/>').strip()

    def deal_res(self, origin_data, res_dict):
        """处理结果"""
        first_bill_no = origin_data["billNo"]
        code = res_dict['code']
        second_bill_no = res_dict.get('second_bill_no', "")  # 真实提单号
        error_msg = res_dict.get('msg')  # 错误信息

        if code == 400:
            # 未查到结果
            self.logger.info(error_msg)
            self.false_msg_list.append(error_msg.strip('<br/>').strip())
            self.false_msg_list.append("\n")
        elif origin_data["delegationNo"].lower() == second_bill_no.lower(): pass  # 委托编号和获取的提单号相同时不处理
        else:
            # 成功
            res_info = f'{first_bill_no} - {second_bill_no}'
            self.success_msg_list.append(res_info.strip('<br/>').strip())
            self.success_msg_list.append("\n")
            self.logger.info(res_info)

            # 消息队列更新
            mq_msg = {
                "bookingId": origin_data["bookingId"],
                "resultContent": second_bill_no,
            }
            self.send_result(msg=mq_msg, tag=2, flag=True, error_info="", bill_no=second_bill_no)
        return True

    def main(self):
        """主函数"""
        time.sleep(2)
        try:
            producer = Thread(target=self.get_tasks)
            producer.start()
            consumer_list = [Thread(target=self.handle_bill_no) for _ in range(CONSUMER_THREAD_NUM)]
            for consumer in consumer_list:
                consumer.start()
            for consumer in consumer_list:
                consumer.join()
            # 发送邮件
            contents = "".join(self.success_msg_list + ['\n'] + self.false_msg_list)
            self.util_mail.send(to=self.email_to, subject=f'CMA加提单号输出结果！{datetime.datetime.now().strftime("%x %X")}', contents=contents)
        except Exception as e:
            message = "".join([str(__file__).rsplit("/", 1)[-1], ":", str(e)])
            send_message_to_developer(message, [])
            logger.error(e, exc_info=True)
        logger.info("check_booking_state运行结束！")


if __name__ == '__main__':
    duck = Telescope()
    duck.main()
