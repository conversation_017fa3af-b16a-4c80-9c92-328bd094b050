# -*- coding: utf-8 -*-
"""
@Date: 2022-01-26
@Description:
        1. 每日0点开始第一次，然后每2h查询一次
        2. 使用多线程进行任务的处理, 1个线程获取API数据, 3个线程在CMA网站进行根据订单号查询数据(多个线程的话会造成查询错误)
        3. 查询CMA网站时, 最多进行5次的查询, 如果有符合条件的查询结果就会停止对当前的订单号进行查询
        4. 将查询结果汇总, 发送到邮箱: <EMAIL>
        5. 使用队列来缓存数据, 设置队列最大值为5个
@Link:
        1. CMA CGM网站链接: https://www.cma-cgm.com.cn/LaraToolkit/BookingInquery
        2. 原型: http://ftp.hgj.net/ftp/海运组/订舱/迭代内容/87 CMA-预配查询--放舱状态/#id=clmdgk&p=查询放舱结果
        3. 测试接口: http://beta-apisix.hgj.com/booking/backend/pass/duck-duck-go/cma-bills
        4. 迭代：https://www.tapd.cn/40271613/prong/tasks/view/1140271613001032665
"""
import json
import re
import time
from datetime import datetime, timedelta
from queue import Queue
from threading import Thread, Lock

from tenacity import retry, stop_after_attempt, wait_fixed

from server import app
from utils.util_proxy import get_proxy
from utils.util_retry import return_after_retry
from utils.util_send_email import SendMail
from utils.util_send_cancel_info import send_error
from utils.util_operate_mysql import MysqlSpiderUtil
from utils import send_request, send_message_to_developer, create_authorization
from conf import SERVER_ENV, HGJ_API_STATE_URL, MAIL_CONFIG, SPIDER_MYSQL_CONFIG, UPLOAD_SEARCH_RESULT_API

# 队列存放的个数
MAX_SIZE = 5
# 每页限制的条数
LIMIT = 10
# 设置消费线程的个数
CONSUMER_THEAD_NUM = 3
# CMA网站订舱信息查询接口
CMA_BOOKING_INQUERY_URL = "https://www.cma-cgm.com.cn/LaraToolkit/BookingInquery/BookingInqueryData"
# CMA网站船期信息查询接口
CMA_SHIP_SCHEDULE_INQUERY_URL = "https://www.cma-cgm.com.cn/LaraToolkit/ShipScheduleInquery/PostListData"
# 查询次数
MAX_CHECK_COUNT = 5
# 通知人邮箱
EMAIL_TO = ['<EMAIL>'] if SERVER_ENV == 'prod' else ['<EMAIL>'] if SERVER_ENV == 'beta' else ['<EMAIL>']
# 记录日志
logger = app.logger
# 拉取指定开头的已确认放舱数据
bill_no_prefix = ["CN", "WM", "CB", "QG", "AC", "CHN", "AHG", "NG", "TJ", "SW", "XI", "FOC", "GG", "DLN", "CPL", "AM",
                  "JI", "ALQ", "APN", "CNC"]
# 拉取的船公司
carried_code_list = ["CMA", "CNC", "CST", "ANL"]


class CheckBookingState:

    def __init__(self):
        self.queue_bill_msg = Queue(maxsize=MAX_SIZE)
        self.queue_check_msg = Queue(maxsize=MAX_SIZE)
        self.stop_tag = 0
        self.util_mail = SendMail(**MAIL_CONFIG)
        self.mysql_spider = MysqlSpiderUtil(**SPIDER_MYSQL_CONFIG)
        # 多线程互斥锁,防止意外数据
        self.lock_sql = Lock()
        self.lock_stop_tag = Lock()
        # 已确认放舱列表
        self.booking_state_put_down = []
        # 船名不一致
        self.booking_state_inconsistent = []
        # 已拒绝订舱
        self.booking_state_cancel = []
        # 订舱处理中
        self.booking_state_handling = []
        # 提醒给客户
        self.booking_state_alert = []
        # 其他情况
        self.booking_state_other = []
        # 订舱处理中情况罗列
        self.booking_state_handling_key = ["感谢您的预订， 此票订舱还在确认中， 如有更新将会第一时间通知您",
                                           "请提供危险品申请资料", "危险品资料相关细节需要和贵司确认",
                                           "请尽快核实并提供正确信息", "等待舱位确认", "处理中"]
        # 提醒客户情况罗列
        self.booking_state_alert_key = [
            "感谢您的预订。抱歉地通知您，由于舱位限制，我们无法确认您的此票订舱。请同您的销售代表或客服咨询是否还有其它备选方案",
            "所订货物重量超出限重", "船名/航次错误，请提供正确的船名航次", "Rate expired",
            "您此票订舱所订航次暂未开放订舱，因此我们无法接受您的订舱, 请于订舱开放后重新提交订舱",
            "根据如下审核规则，您此票Booking所用约号的订约方和该Booking 所涉相关方不符, 请使用正确约号或完整booking 相关方信息重新提交订舱",
            "请提供正确的约号",
            "感谢您的预定，抱歉地通知您， 由于目前的市场状况，我们无法确认此订舱。建议您尝试申请Spot On 产品订舱来获取舱位。具体信息详见我司官网或者咨询我司销售代表进行了解",
            "请确认货物属性并提供相关文件"]

    @staticmethod
    @retry(stop=stop_after_attempt(3), wait=wait_fixed(5), retry_error_callback=return_after_retry)
    def upload_result(url, data):
        """向订舱后端请求数据"""
        headers = {"authorization": create_authorization("booking")}
        kw = {"headers": headers, "topic": "json", "timeout": 10, "data": data}
        response = send_request(url, method="post", **kw)
        if response.status_code == 200:
            return response
        else:
            response.raise_for_status()

    def filter_repeat_send(self, bill_no, message_type, content, carrier_code):
        """将数据去重，避免重复发送"""
        try:
            with self.lock_sql:
                search_sql = "SELECT billNo, message_type, content FROM booking_release_state_info WHERE billNo=%s AND message_type=%s AND carrier_code=%s"
                search_result = self.mysql_spider.get_one(search_sql, (bill_no, message_type, carrier_code))
                if search_result:
                    if content == search_result[-1]: return False
                    update_sql = "UPDATE booking_release_state_info SET content=%s WHERE billNo=%s"
                    self.mysql_spider.update(update_sql, (content, bill_no))
                    return True
                else:
                    insert_sql = "INSERT INTO booking_release_state_info (billNo, message_type, content, carrier_code) VALUES (%s, %s, %s, %s)"
                    self.mysql_spider.update(insert_sql, (bill_no, message_type, content, carrier_code))
                    return True
        except Exception as e:
            logger.error(e, exc_info=True)
            return False

    def get_data(self):
        """
        通过海管家订舱API获取放舱相关数据，获取已发送船公司待放舱和已放舱但未截单的数据
        """
        for status in [{"bookingStatuses": ["SENT_TO_SHIPPING_COMPANY_FOR_RELEASE"], "supplementStatuses": []},
                       {"bookingStatuses": ["SHIPPING_COMPANY_RELEASE"], "supplementStatuses": ["NO_SUBMITTED", "REJECTED"]}]:
            page_num, page_size = 1, 20
            while True:
                data = {
                    "pageNum": page_num,
                    "pageSize": page_size,
                    "carrierCodes": carried_code_list,
                    "businessPortCodes": [],
                    "billNoSpecialMarks": bill_no_prefix,
                    "bookingStatuses": status["bookingStatuses"],
                    "supplementStatuses": status["supplementStatuses"],
                }
                response = self.upload_result(HGJ_API_STATE_URL, data)
                if not response:
                    message = "".join([str(__file__).rsplit("/", 1)[-1], ":", "调用API接口失败!!!"])
                    send_message_to_developer(message, [])
                    logger.warning(f"请求接口获取数据失败: 调用API接口失败!!!")
                else:
                    response_json = response.json()
                    data = response_json["data"]
                    for one in data["records"]:
                        self.queue_bill_msg.put(one)
                        logger.info(f"放入队列订舱单号信息: {one}")

                    # 请求下一页
                    if page_num * page_size < response_json["data"]["total"]:
                        page_num += 1
                    else:
                        break
        for i in range(CONSUMER_THEAD_NUM):
            self.queue_bill_msg.put(None)

    def check_booking_status(self):
        """
        订舱信息查询，使用订舱单号
        """
        while True:
            one = self.queue_bill_msg.get()
            if not one and self.queue_bill_msg.qsize() >= CONSUMER_THEAD_NUM:
                self.queue_bill_msg.put(one)
                continue
            if not one and self.queue_bill_msg.qsize() < CONSUMER_THEAD_NUM:
                self.queue_check_msg.put(None)
                break
            logger.info(f"当前提取待订舱查询信息: {one}")
            bill_no = one["billNo"]
            kw = {
                "data": {
                    "BookingNo": bill_no,
                    "CusRef": ""
                },
                "topic": "form",
                "mask": True
            }
            raw_message = {}
            for i in range(MAX_CHECK_COUNT):
                kw["proxy"] = get_proxy("pz")
                if not kw["proxy"]:
                    continue
                try:
                    response = send_request(url=CMA_BOOKING_INQUERY_URL, method="post", **kw)
                    if response:
                        raw_message = response.json()
                        data = raw_message.get("data")
                        if data and "查询失败" not in response.text:
                            # 匹配订舱结果提示信息
                            re_result = re.search("<p.*?>(.*?)</p>", data, re.S)
                            message = re_result.group(1) if re_result else "数据匹配失败, 联系爬虫一下"
                            break
                except Exception as e:
                    logger.warning(e, exc_info=True)
            else:
                message = f"查询{MAX_CHECK_COUNT}次未获取到正确响应数据"

            message = message.split("\n")[0].strip("<br/>")
            self.handle_booking_status_message(message=message, bill_info=one, raw_message=raw_message)

    def handle_booking_status_message(self, message, bill_info, raw_message):
        """
        处理订舱信息查询结果信息
        :params message: 待处理的信息
        :params bill_info: 队列取出原始数据
        :params raw_message: 查询到的原始数据
        :return:
        """
        bill_no = bill_info["billNo"]

        # 发送取消提单
        data = {"carrier": bill_info["carrierCode"], "billNo": bill_info["billNo"],
                "vessel": bill_info["vessel"], "voyageNo": bill_info["voyageNo"]}
        send_error(data, message)

        if "确认预配" in message:
            # 当海管家订舱状态为待放舱状态时发送邮件，且当船公司为CMA船号不以NGP开头时将查询到的预配数据插入到数据库中
            if bill_info.get("bookingStatus") == "SENT_TO_SHIPPING_COMPANY_FOR_RELEASE":
                self.booking_state_put_down.append("--".join([bill_no, message]))
                bill_info["carrierCode"].upper() == "CMA" and not bill_no.startswith("NGP") and self.insert_data(bill_no, raw_message)
            # 提取船名航次航线
            vessel, voyage_no, voyage_line = self.get_vessel(raw_message)
            # 去除空格和横线后比对
            if re.sub(r"[ -]", "", vessel) != re.sub(r"[ -]", "", bill_info["vessel"]):
                msg = f"{bill_info['billNo']}--订舱：{bill_info['vessel']}/{bill_info['voyageNo']}--放舱：{vessel}/{voyage_no}"
                self.booking_state_inconsistent.append(msg)
            # 加入船期查询队列等待消费
            self.queue_check_msg.put(
                {"vessel": vessel, "voyage_no": voyage_no, "voyage_line": voyage_line, "bill_info": bill_info})
        elif "取消" in message:
            self.booking_state_cancel.append("--".join([bill_no, message]))
        elif list(filter(lambda x: x in message, self.booking_state_handling_key)):  # 订舱处理中
            self.booking_state_handling.append("--".join([bill_no, message]))
        elif list(filter(lambda x: x in message, self.booking_state_alert_key)):  # 需要转发客户
            # 精确发送消息
            message = list(filter(lambda x: x in message, self.booking_state_alert_key))[0]
            self.booking_state_alert.append("--".join([bill_no, message]))
            message_type = "SHIPPING_RELEASE_FAIL"
            # 向订舱传输查询结果
            if self.filter_repeat_send(bill_no, message_type, message, bill_info["carrierCode"]):
                pyload = {
                    "pythonOperateType": "SHIP_FEEDBACK_EMAIL",
                    "messageType": message_type,
                    "billNo": bill_no,
                    "content": message
                }
                self.upload_result(UPLOAD_SEARCH_RESULT_API, pyload)
        else:
            self.booking_state_other.append("--".join([bill_no, message]))

    def check_ship_schedule(self):
        """船期查询"""
        while True:
            one = self.queue_check_msg.get()
            if not one:
                with self.lock_stop_tag:
                    self.stop_tag += 1
                    if self.stop_tag == CONSUMER_THEAD_NUM:
                        break
                continue
            logger.info(f"当前提取待船期查询信息: {one}")
            kw = {
                "data": {
                    "vessel": one.get("vessel"),
                    "voyage": one.get("voyage_no"),
                    "service": "",
                    "pol": one["bill_info"].get("portOfLoadingCode"),
                    "area": "",
                    "etdDateFrom": (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d"),
                    "etdDateTo": (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d"),
                    "line": "",
                    "Draw": "3",
                    "Page": "1",
                    "Rows": "10",
                    "order": "asc",
                    "sort": ""
                },
                "topic": "form",
                "mask": True
            }
            for i in range(MAX_CHECK_COUNT):
                kw["proxy"] = get_proxy("pz")
                if not kw["proxy"]:
                    continue
                try:
                    response = send_request(url=CMA_SHIP_SCHEDULE_INQUERY_URL, method="post", **kw)
                    if response:
                        raw_message = response.json()
                        data = raw_message.get("data")
                        if data and "查询失败" not in response.text:
                            # 匹配船期信息
                            message = next(
                                (d for d in data if d.get("VesselCode", "").upper() == one.get("vessel", "").upper()),
                                "返回结果匹配失败")
                            break
                except:
                    pass
            else:
                message = f"查询{MAX_CHECK_COUNT}次未获取到正确响应数据"
            self.handle_ship_schedule_message(message=message, check_msg=one)

    def handle_ship_schedule_message(self, message, check_msg):
        """处理船期查询结果"""
        pyload = {
            "pythonOperateType": "CMA_VESSEL_SCHEDULE_NOTIFY",
            "billNo": check_msg["bill_info"].get("billNo"),
            "businessPortCode": check_msg["bill_info"].get("businessPortCode"),
            "vessel": check_msg.get("vessel"),
            "voyageNo": check_msg.get("voyage_no"),
            "voyageLine": check_msg.get("voyage_line"),
            "portOfLoadingCode": check_msg["bill_info"].get("portOfLoadingCode"),
            "carrierCode": check_msg["bill_info"].get("carrierCode")}
        if isinstance(message, dict):
            if message.get("POLName") == "NINGBO" and message.get("StateAgentName") == "兴港船代":
                if message.get("TerminalName") == "甬舟":
                    shipping_agent = "舟山兴港国际船舶代理有限公司"
                else:
                    shipping_agent = "宁波兴港国际船舶代理有限公司"
            else:
                shipping_agent = message.get("StateAgentName")
            pyload_other = {
                "successFlag": True,
                "etd": self.__convert_time(message.get("EtdDate")).split(" ")[0],  # ETD时间
                "esiSiTime": self.__convert_time(message["SiCutOffDate"]) if message.get("SiCutOffDate") else message.get("SiCutOff"),  # ESI截单时间
                "etb": self.__convert_time(message.get("EtbDate")).split(" ")[0],  # ETB时间
                "vgmCutOffTime": message.get("VgmCutOff"),  # VGM截止时间
                "shippingAgent": shipping_agent,  # 船代
                "remark": None,
            }
        else:
            pyload_other = {
                "successFlag": False,
                "etd": None,
                "esiSiTime": None,
                "etb": None,
                "vgmCutOffTime": None,
                "shippingAgent": None,
                "remark": message,
            }
        pyload.update(pyload_other)
        self.upload_result(UPLOAD_SEARCH_RESULT_API, pyload)

    @staticmethod
    def __convert_time(time_str):
        time_str = time_str if time_str else ""
        """提取标准日期格式"""
        re_time = re.search(r"/Date\((\d+?)\)/", time_str)
        return str(datetime.fromtimestamp(int(re_time.group(1)) / 1000)) if re_time else ""

    @staticmethod
    def get_vessel(raw_message):
        """提取船名航次航线"""
        data = raw_message.get("data", "")
        re_vessel = re.search(r"船名[:：](.*?)<br/>", data, re.S)
        vessel = re_vessel.group(1).strip() if re_vessel else ""
        re_voyage = re.search(r"航次[:：](.*?)<br/>", data, re.S)
        voyage_no = re_voyage.group(1).strip() if re_voyage else ""
        re_voyage_line = re.search(r"航线[:：](.*?)<br/>", data, re.S)
        voyage_line = re_voyage_line.group(1).strip() if re_voyage_line else ""
        return vessel, voyage_no, voyage_line

    def send_message(self):
        """拼接信息, 发送邮件"""
        subject = f'CMA/CNC/CST/ANL--是否放舱查询结果通知--{time.strftime("%Y年%m月%d %H时%M分")}'
        self.booking_state_put_down.insert(0, "<b style='font-size: 16px'>以下查询结果为已确认放舱，请及时上传配舱回单</b>")
        if len(self.booking_state_put_down) == 1:
            self.booking_state_put_down.append("[ 空 ]")
        self.booking_state_inconsistent.insert(0, "<br><b style='font-size: 16px'>以下查询结果订舱和放舱船名不一致，疑似换船，请注意更新！</b>")
        if len(self.booking_state_inconsistent) == 1:
            self.booking_state_inconsistent.append("[ 空 ]")
        self.booking_state_cancel.insert(0, "<br><b style='font-size: 16px'>以下查询结果为已拒绝订舱，请及时更新状态</b>")
        if len(self.booking_state_cancel) == 1:
            self.booking_state_cancel.append("[ 空 ]")
        self.booking_state_handling.insert(0, "<br><b style='font-size: 16px'>以下查询结果为订舱处理中，请耐心等待</b>")
        if len(self.booking_state_handling) == 1:
            self.booking_state_handling.append("[ 空 ]")
        self.booking_state_alert.insert(0, "<br><b style='font-size: 16px'>以下查询结果为转发客户，请注意查看收件箱</b>")
        if len(self.booking_state_alert) == 1:
            self.booking_state_alert.append("[ 空 ]")
        self.booking_state_other.insert(0, "<br><b style='font-size: 16px'>以下查询结果为其他情况，请酌情处理</b>")
        if len(self.booking_state_other) == 1:
            self.booking_state_other.append("[ 空 ]")
        contents = "<br>".join(self.booking_state_put_down + self.booking_state_inconsistent +
                               self.booking_state_cancel + self.booking_state_alert +
                               self.booking_state_handling + self.booking_state_other)
        self.util_mail.send(to=EMAIL_TO, subject=subject, contents=contents)

    def insert_data(self, bill_no, message):
        """
        向数据库插入已放舱数据
        :param bill_no: 提单号
        :param message: 确认放舱的原始数据
        :return:
        """
        # 判断是否当前单号已存在
        check_sql = "SELECT billNo FROM cma_bc_info WHERE billNo=%s"
        check_result = self.mysql_spider.get_one(check_sql, bill_no)
        if not check_result:
            insert_sql = "INSERT INTO cma_bc_info (billNo, cmaConfirmationData) VALUES (%s, %s)"
            self.mysql_spider.update(insert_sql, params=(bill_no, json.dumps(message, ensure_ascii=False)))

    def main(self):
        """主函数"""
        try:
            producer = Thread(target=self.get_data)
            producer.start()
            consumer_list = [Thread(target=self.check_booking_status) for _ in range(CONSUMER_THEAD_NUM)]
            for consumer in consumer_list:
                consumer.start()
            consumer_check = Thread(target=self.check_ship_schedule)
            consumer_check.start()
            for consumer in consumer_list:
                consumer.join()
            consumer_check.join()
            # 发送邮件
            self.send_message()
        except Exception as e:
            message = "".join([str(__file__).rsplit("/", 1)[-1], ":", str(e)])
            send_message_to_developer(message, [])
            logger.error(e, exc_info=True)
        logger.info("check_booking_state运行结束！")


if __name__ == '__main__':
    cbs = CheckBookingState()
    cbs.main()
    # cbs.handle_ship_schedule_message()
