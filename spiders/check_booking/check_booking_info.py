# -*- coding: utf-8 -*-
"""
@Date: 2021-12-15
@Description:
        1. 使用定时任务在每天的12点对CMA CGM网站已定CMA船司的预配箱信息进行查询
        2. 使用多线程进行任务的处理, 1个线程获取API数据, 1个线程在航华网站进行根据订单号查询数据(多个线程的话会造成查询错误)
        3. 查询航华网站时, 最多进行8次的查询, 如果有符合条件的查询结果就会停止对当前的订单号进行查询
        4. 将查询到数据不一致, 订单号查询失败, 订单取消的状态发送到群里
        5. 使用队列来缓存数据, 设置队列最大值为5个
@Link:
        1. CMA CGM网站链接: https://www.cma-cgm.com.cn/LaraToolkit/BookingInquery
        2. 原型: http://ftp.hgj.net/ftp/海运组/订舱/迭代内容/80 CMA-查询预配是否有变--爬虫需求/#id=clmdgk&p=cma-查询预配是不是有问题
        3. 接口文档: https://beta-ingress.yunlsp.com/booking/backend/doc.html#/default/%E9%B8%AD%E9%B8%AD%E5%87%BA%E5%87%BB!/duckCheckBookingInfoUsingGET
"""
import re
import time

from server import app

from queue import Queue
from threading import Thread

import requests
from retry.api import retry_call

from conf import HGJ_API_URL, SERVER_ENV
from utils.util_proxy import get_proxy
from utils import send_message_to_developer, send_message_to_group, send_request, create_authorization

logger = app.logger

# 定义常量
# CMA CGM网站的链接
CHECK_BOOKING_URL = "https://www.cma-cgm.com.cn/LaraToolkit/BookingInquery/BookingInqueryData"
# 定义重试的次数
MAX_CHECK_TIME = 8
# 定义队列的最大个数
MAX_SIZE = 5
# 定义消费线程的个数
THREAD_NUM = 1
# 调用API设置每页查询的个数
LIMIT = 100


def request(url, method="get", **kwargs) -> requests.Response:
    """发送请求"""
    default_setting = {"method": method, "timeout": 10}
    default_setting.update(kwargs)
    try:
        response = retry_call(send_request, [url], default_setting, tries=1)
    except:
        response = None
    return response


def send_check_info_msg_to_wechat(flag, message):
    """
    发送查询信息到企业微信
    :param flag: 设置是否发送信息到企业微信中 True 发送, False 不发送
    :param message: 需要发送的信息
    :return: 成功 True, 失败 False
    """
    if SERVER_ENV in ['dev', 'local']:
        return True
    cur_server_env = '测试' if SERVER_ENV != 'prod' else SERVER_ENV
    message = cur_server_env.center(30, '=') + '\n' + message + '\n' + "END".center(30, '=')
    mentioned_mobile_list = []
    if flag:
        # CMA 通知人: 季佳妮、胡院洁
        mentioned_mobile_list = ["13621824573", "13381948382"]
    send_message_to_group(message, contact=mentioned_mobile_list)


def get_check_booking_info(info: list, relation_dict: dict) -> dict:
    """
    将查询的数据转为字典
    :param info: 传入的查询到的数据
    :param relation_dict: 字段对应关系字典
    :return: 返回解析好的数据
    """
    check_booking_info_dict = {}
    # 记录预配箱的个数
    count = 0
    # 记录是否有预配箱
    container_flag = False

    pattern = re.compile("^(.*?): (.*?(\\()?(?(3)代码：(\\w{5})|$))")
    for one in info:
        re_res = pattern.match(one)
        if not re_res:
            continue
        elif "尺寸" in one:
            container_flag = True
            count += 1
            container_info = one.split("  ")
            for container_params in container_info:
                key, value = container_params.split(":")
                check_booking_info_dict[relation_dict[key.strip()]] = check_booking_info_dict.get(relation_dict[key.strip()], []) + [str(value).strip()]
        else:
            code = re_res.group(pattern.groups)
            name = re_res.group(1).strip()
            if name not in relation_dict:
                continue
            if code:
                check_booking_info_dict[relation_dict[name]] = code.strip()
            else:
                check_booking_info_dict[relation_dict[name]] = re_res.group(2).strip()

    check_booking_info_dict["containerSize"] = "".join(sorted(check_booking_info_dict["containerSize"], key=lambda j: int(j)))
    check_booking_info_dict["containerType"] = "".join(sorted(check_booking_info_dict["containerType"]))
    check_booking_info_dict["containerNumber"] = "".join(sorted(check_booking_info_dict["containerNumber"], key=lambda j: int(j)))
    check_booking_info_dict["container_count"] = count
    check_booking_info_dict["container_info"] = container_flag

    return check_booking_info_dict


class BookingInfoCheck(object):

    def __init__(self):
        self.check_booking_relation_dict = {"目的港名称": "portOfDischarge", "目的地名称": "placeOfDelivery", "船名": "vessel",
                                            "尺寸": "containerSize", "航次": 'voyageNo', "航线": "routeCode",
                                            "类型": "containerType", "箱量": "containerNumber", "订舱单号": "billNo",
                                            "开船日期": "estimatedTimeOfDeparture"}
        # 需要查询的船司
        self.carrier_code_list = ["CMA", "CNC", "CST", "ANL"]
        # 创建队列
        self.queue = Queue(maxsize=MAX_SIZE)
        # 海管家箱型和inttra不同之处参照表
        self.container_type_hgj_2_intter = {
            "NOR": "HC",
            "GP": "ST",
            "HQ": "HC"
        }

    def get_info(self):
        """
        根据API请求得到的订单号在CMA CGM网站进行查询
        :return:
        """
        while True:
            # 获取数据成功还是失败
            flag = True
            # 定义返回的信息
            message = ""
            value = self.queue.get()
            if value is None and self.queue.qsize() >= THREAD_NUM:
                self.queue.put(value)
                continue
            if value is None and self.queue.qsize() < THREAD_NUM:
                break
            # 获取订单编号
            booking_no = list(value.keys())[0]
            container_info = value[booking_no]

            params = {
                "url": CHECK_BOOKING_URL,
                "method": "post",
                "headers": {"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"},
                "data": {
                    "BookingNo": booking_no,
                    "CusRef": ""
                },
                "proxy": get_proxy("pz")
            }
            for i in range(MAX_CHECK_TIME):
                response = request(**params)
                if response:
                    try:
                        data = response.json().get("data")
                        pattern = re.compile("<p .*?>(.*?)</p>", re.S)
                        result = pattern.match(data)
                        info = result.group(1) if result else ""
                        info = info.replace("<br/>", "") if info else info
                        if "确认预配" in info:
                            # 替换空格
                            data = data.replace("&#9;", " ")
                            # 切割信息
                            data_list = data.split("<br/>")[1:]
                            check_booking_info_dict = get_check_booking_info(
                                info=data_list,
                                relation_dict=self.check_booking_relation_dict)
                            compare_result = self.compare_info(api_data=container_info,
                                                               check_booking_data=check_booking_info_dict)
                            # 只有在有错误的情况下向企业微信发送消息
                            if compare_result:
                                message = "查询错误说明:\n" + compare_result
                                data_list.insert(0, f"查询结果：{info}")
                                message += "\n".join(data_list)
                            else:
                                flag = False
                            break
                        elif "订舱取消" in info or "订舱正在处理中" in info:
                            # 替换空格
                            data = data.replace("&#9;", " ")
                            # 切割信息
                            data_list = data.split("<br/>")[1:]
                            data_list.insert(0, f"查询结果：{info}")
                            message = f"查询错误说明:\n{info}\n" + "\n".join(data_list)
                            break
                        elif "订舱" in info:
                            message = "\n".join(["查询错误说明:", f"提单号: {booking_no}", info])
                            break
                        else:
                            message = "\n".join(["查询错误说明:", f"提单号: {booking_no}", info]) if info else info
                            time.sleep(3+i*0.5)
                    except Exception as e:
                        error_message = "".join([str(__file__).rsplit("/", 1)[-1], ":", str(e)])
                        send_message_to_developer(error_message, [])
                        logger.error(e, exc_info=True)
            else:
                if not message:
                    message = f"查询错误说明:\n查询失败!未查询到预订单号: {booking_no}的相关信息!!!"

            if flag:
                message = message.replace("</p>\n", "")
                # 向企业微信发送错误信息
                send_check_info_msg_to_wechat(flag=flag, message=message)

    def get_data(self):
        """
        通过API请求数据
        :return:
        """
        for carrier_code in self.carrier_code_list:
            # 定义初始页数
            current_page = 1
            while True:
                data = {
                    "carrierCode": carrier_code,
                    "pageNum": current_page,
                    "pageSize": LIMIT,
                    "businessPortCode": "SHANGHAI"
                }
                params = {
                    "url": HGJ_API_URL,
                    "method": "post",
                    "topic": "json",
                    "data": data,
                    "headers": {"authorization": create_authorization("booking")}
                }
                try:
                    response = request(**params)
                    if not response:
                        raise KeyError("调用API接口失败!!!")
                    response_json = response.json()
                    data = response_json["data"]
                except Exception as e:
                    message = "".join([str(__file__).rsplit("/", 1)[-1], ":", str(e)])
                    send_message_to_developer(message, [])
                    logger.error(f"请求接口获取数据失败: {e}", exc_info=True)
                    break
                else:
                    # 提取数据放入到队列
                    data = data.get("records", [])
                    for one in data:
                        api_data = {}
                        temp_data = {}      # 临时存储字典数据
                        # 获取提单号
                        bill_no = one["billNo"]
                        vessel_name = one["vessel"]
                        voyage_no = one["voyageNo"]
                        container_info = one["carrierContainerList"]

                        temp_data["vessel"] = vessel_name  # 设置船名
                        temp_data["voyageNo"] = voyage_no  # 设置航次

                        if len(container_info) < 1:
                            temp_data["container_info"] = False         # 设置提示没有预配箱信息
                            temp_data["containerNumber"] = 0            # 设置提示没有预配箱信息个数
                            api_data[bill_no] = temp_data
                        else:
                            temp_data["container_info"] = True  # 设置提示有预配箱信息
                            count = 0                          # 记录预配箱个数
                            for container in container_info:
                                count += 1
                                container_size_type = container.get("containerType", "")
                                # 将不一致的箱型进行替换
                                container_size = container_size_type[:2]
                                container_type = container_size_type[2:]
                                if container_type in self.container_type_hgj_2_intter.keys():
                                    container_type = self.container_type_hgj_2_intter[container_type]
                                temp_data["containerSize"] = temp_data.get("containerSize", []) + [int(container_size)]
                                temp_data["containerType"] = temp_data.get("containerType", []) + [container_type]
                                temp_data["containerNumber"] = temp_data.get("containerNumber", []) + [int(container["containerNumber"])]

                            temp_data["container_count"] = count
                            temp_data["containerSize"] = "".join([str(i) for i in sorted(temp_data["containerSize"])])
                            temp_data["containerType"] = "".join(sorted(temp_data["containerType"]))
                            temp_data["containerNumber"] = "".join([str(i) for i in sorted(temp_data["containerNumber"])])
                            api_data[bill_no] = temp_data
                        self.queue.put(api_data)
                    # 获取总个数
                    total = response_json.get("total", 0)
                    divisor, remainder = divmod(total, LIMIT)
                    pages = divisor+1 if remainder > 0 else divisor + 1 if not divisor else divisor
                    if current_page == pages:
                        break
                    else:
                        current_page += 1

        for i in range(THREAD_NUM):
            self.queue.put(None)

    @staticmethod
    def compare_info(api_data: dict, check_booking_data: dict) -> str:
        """
        比较数据
        :param api_data: 从API请求得到的数据
        :param check_booking_data: 从CMA CGM网站得到的数据
        :return: 返回比对信息
        """
        error_info = ""
        # 参与比较的值
        compare_value_dict = {"船名": "vessel", "航次": "voyageNo", "尺寸": "containerSize",
                              "类型": "containerType", "箱量": "containerNumber"}

        # 比较是否都包含预配箱
        if not all([api_data["container_info"], check_booking_data["container_info"]]):
            error_info = "预配箱信息不匹配\n"
            return error_info
        if api_data["container_count"] != check_booking_data["container_count"]:
            error_info = "预配箱数量不一致\n"
            return error_info
        for key, value in compare_value_dict.items():
            if api_data[value] != check_booking_data[value]:
                error_info += f"{key}数据的不一致, 查询数据: {check_booking_data[value]}, 原始数据: {api_data[value]}\n"

        return error_info

    def main(self):
        """主函数"""
        time.sleep(2)
        producer = Thread(target=self.get_data)
        producer.daemon = True
        producer.start()
        consumer_list = [Thread(target=self.get_info) for i in range(THREAD_NUM)]
        for consumer in consumer_list:
            consumer.start()
        for consumer in consumer_list:
            consumer.join()
        logger.info("check_booking_info运行结束！")


if __name__ == '__main__':
    check = BookingInfoCheck()
    check.main()

