# -*- coding: utf-8 -*-
"""
数据交互中心
1.查询数据库已放舱的单号
2.更新查到的数据单号
"""
import time
import json

from server import app
from utils import send_request, send_message_to_developer
from utils.util_operate_mysql import MysqlSpiderUtil
from spiders.auto_upload_bc.config import download_pdf_link
from conf import SPIDER_MYSQL_CONFIG, SERVER_ENV


class HandleData:

    def __init__(self):
        self.mysql_spider = MysqlSpiderUtil(**SPIDER_MYSQL_CONFIG)

    def filter_data(self):
        """
        查询数据库获取数据在更新时间在2小时以内而且是未发送过的数据
        :return:
        """
        sql = "SELECT billNo FROM cma_bc_info WHERE createTime >= %s AND ((sent=%s AND message IS NULL) OR message like '%%未检索到相关BC，请核实%%')"
        bill_no_list = self.mysql_spider.get_all(sql, (time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time()-48*60*60)), 0))
        bill_no_list = list(map(lambda x: x[0], bill_no_list))
        app.logger.info(str(bill_no_list))
        if not bill_no_list: return
        data = {"env": SERVER_ENV, "billNoList": bill_no_list}
        response = send_request(download_pdf_link, method="post", data=data, topic="json")
        if response.status_code != 200:
            send_message_to_developer("cma查询网站信息失败", contact=[])

    def insert_data_to_mysql(self, params: dict):
        """
        将数据更新到mysql数据库
        :return:
        """
        sql = "UPDATE cma_bc_info SET billNo=%s, pdfDownloadLink=%s, pdfPreviewLink=%s, message=%s WHERE billNo=%s"
        self.mysql_spider.update(sql, params=(params["bill_no"], params["booking_receipt"], params["preview_url"], json.dumps(params, ensure_ascii=False), params["bill_no"]))


if __name__ == '__main__':
    d = HandleData()
    d.filter_data()
