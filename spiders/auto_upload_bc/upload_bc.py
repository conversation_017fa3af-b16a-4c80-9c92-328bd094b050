# -*- coding: utf-8 -*-
"""
上传bc
"""
import json

from spiders.base import Base
from spiders.auto_upload_bc.config import url_raw
from spiders.auto_upload_bc.params import Prams
from utils.util_operate_mysql import Mysql<PERSON>piderUtil
from utils import send_message_to_developer, create_authorization
from conf import SPIDER_MYSQL_CONFIG


class UploadBc(Base):

    def __init__(self):
        super().__init__()
        self.params = Prams()
        self.mysql_spider = MysqlSpiderUtil(**SPIDER_MYSQL_CONFIG)

    def get_unsent_data(self, start_time, end_time, sent=0):
        """
        获取未上传的bc
        过滤数据
            1. 数据的message字段是有值的
            2. 数据是未发送的
            3. 数据在查询范围内的
        :param start_time: 查询数据的开始范围
        :param end_time: 查询数据的结束范围
        :param sent: 1:已发送，0:未发送
        :return:
        """
        try:
            sql = "SELECT billNo, message FROM cma_bc_info WHERE message IS NOT NULL AND sent=%s AND createTime BETWEEN %s AND %s"
            data = self.mysql_spider.get_all(sql, params=(sent, start_time, end_time))
            # 打印查询结果
            self.logger.info(f"状态：{sent},开始时间：{start_time},结束时间：{end_time}")
            self.logger.info(str(data))

            for one in data:
                json_data = json.loads(one[1])
                if json_data["error_info"]:
                    pyload = self.params.replace_error_field(json_data)
                    api_type = "error"
                else:
                    pyload = self.params.replace_upload_field(json_data)
                    api_type = "upload"
                self.upload_message(api_type, pyload) and self.update_sent_status(one[0])
        except Exception as e:
            self.logger.error(e, exc_info=True)
            send_message_to_developer("CMA上传bc失败：" + str(e), contact=[])

    def update_sent_status(self, bill_no: str):
        """
        更新发送状态
        :param bill_no: 提单哈
        :return:
        """
        sql = "UPDATE cma_bc_info SET sent=%s WHERE billNo=%s"
        self.mysql_spider.update(sql, params=(1, bill_no))

    def upload_message(self, api_type, one_data: str) -> bool:
        """
        将bc数据上传到后台
        :param api_type: 接口类型
        :param one_data: 数据
        :return: 上传成功为True，否则为False
        """
        headers = {
            "authorization": create_authorization("booking")
        }
        response = self.request(url_raw[api_type], method="post", headers=headers, data=one_data, topic="json")
        if response.json()["code"] == 200:
            return True
        return False


if __name__ == '__main__':
    upload = UploadBc()
    upload.get_unsent_data(start_time="2024-02-27", end_time="2024-02-28")
