# -*- coding: utf-8 -*-
import os
from urllib.parse import urljoin

from conf import SERVER_ENV, PROJECT_PATH


setting_path = os.path.join(PROJECT_PATH, "spiders/auto_upload_bc", "settings.ini")


# 网站链接
cma_url = "https://www.cma-cgm.com/ebusiness/document"

# BC上传接口
# 处理邮件分拣结果接口前缀
URL_PREFIX = {
    "local": "https://dev-apisix.hgj.com",
    "dev": "https://dev-apisix.hgj.com",
    "beta": "https://beta-apisix.hgj.com",
    "uat": "http://uat-ingress-ng.hgj.com",
    "prod": "http://ingress-ng.hgj.com"
}.get(SERVER_ENV, "dev")

# bc接口
url_raw = {
    "upload": urljoin(URL_PREFIX, "/booking-common-channel/aksk/rpa/execute/uploadReceipt"),
    "modify": urljoin(URL_PREFIX, "/booking-common-channel/aksk/rpa/execute/updateBillNo"),
    "cancel": urljoin(URL_PREFIX, "/booking-common-channel/aksk/rpa/execute/refuseBooking"),
    "error": urljoin(URL_PREFIX, "/booking-common-channel/aksk/rpa/execute/sendMsg"),
    "updateAndDeclined": urljoin(URL_PREFIX, "/booking-common-channel/aksk/rpa/execute/updateBillNoAndReject")
}

# cma网站拉取工具接口
download_pdf_link = "https://beta-callback.hgj.com/new-ingress/booking-cma-upload-bc-spider/getAttachment"
