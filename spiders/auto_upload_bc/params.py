# -*- coding: utf-8 -*-
"""
参数媒介
"""
from datetime import datetime
from conf import WEBSITE_INFO


class Prams(object):

    __slots__ = ()

    @staticmethod
    def replace_upload_field(raw_data):
        """
        将字段转为接口字段数据, 上传bc字段
        :param raw_data: json数据
        :return: 替换后的数据
        """
        data = {}
        compare_field_dict = {
            'carrier': 'carrier', 'port_of_discharge': 'portOfDischarge', 'place_of_delivery': 'placeOfDelivery',
            'box_volume': "boxVolume", 'bill_no': 'billNo', 'vessel': 'vessel', 'voyage_no': 'voyageNo',
            'etd_date': 'etd', 'source': 'source', 'website': 'website', 'website_name': 'websiteName',
            'attachment_subject': 'attachmentSubject', 'booking_receipt': 'bookingReceipt', 'port_of_loading': 'portOfLoading',
            'terminal': 'terminal', 'vgm_cut_off_date': 'vgmCutOffDate', 'port_cut_off_date': 'portCutOffDate',
            'si_cut_off_date': 'siCutOffDate', "email_annex": "emailAnnex"}
        for key, value in raw_data.items():
            if key not in compare_field_dict:
                continue
            data[compare_field_dict[key]] = value
        return data

    @staticmethod
    def replace_error_field(raw_data):
        """
        将字段转为接口字段数据, 发送报错信息
        :param raw_data: json数据
        :return: 替换后的数据
        """
        data = {}
        compare_field_dict = {
            'carrier': 'carrier', 'port_of_discharge': 'portOfDischarge', 'place_of_delivery': 'placeOfDelivery',
            'box_volume': 'boxVolume', 'source': 'source', 'vessel': 'vessel', 'voyage_no': 'voyageNo',
            'etd_date': 'etd', 'bill_no': 'billNo', 'error_info': 'reason',
            'si_cut_off_date': 'siCutOffDate', "email_annex": "emailAnnex"}
        for key, value in raw_data.items():
            if key not in compare_field_dict:
                continue
            data[compare_field_dict[key]] = value
        # 设置消息类型
        data["msgType"] = "RPA_BOOKING_ERROR"
        return data

