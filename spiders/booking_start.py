# -*- coding: utf-8 -*-
"""
inttra
船公司：CMA、CNC、ANL

航华
船公司：EMC（EVG）、ONE、MCC、HMM（已迁移）

华东外运
船公司： MSC、TSL、WHL（已迁移）
"""
import json
import time

from hgj_loguru import hgj_logger
from utils import send_message_to_developer
from utils.util_rocketmq import ConnRocket
from utils.util_selenium import SeleniumBaseSpider
from spiders.inttra_booking.inttra import InttraSpider
from utils.util_redis import RedisUtil
from conf import MQ_SUBSCRIBE_ID, MQ_TOPIC, INTTRA_SUPPORT_CARRIER_LIST, MQ_SUBSCRIBE_TAG

selenium_obj = SeleniumBaseSpider(hgj_logger)
# # 是否需要开启无头模式，默认是开启
# selenium_obj.headless = False


class HandleMQ(object):

    def __init__(self):
        self.rocket = ConnRocket()

    def go(self):
        self.rocket.push_message(
            group_id=MQ_SUBSCRIBE_ID,
            topic=MQ_TOPIC,
            func=self.deal,
            expression=MQ_SUBSCRIBE_TAG
        )

    @staticmethod
    def deal(msg):
        # 每次重试2次
        count = 1
        while count < 3:
            browser, service = selenium_obj.create_browser()
            try:
                if not isinstance(msg, dict):
                    message_id = msg.id
                    msg = json.loads(msg.body)
                    detail = msg["detail"].replace(' ', '').replace(r'\xa0', '').replace(r'\u2028', '').replace(
                        r'\u2029', '').replace(' ', ' ').replace('\t', '').replace('\\t', '')
                    msg["detail"] = json.loads(detail)
                    msg["messageId"] = message_id
                else:
                    msg["messageId"] = msg.get("messageId", None) and msg["messageId"] or str(int(time.time()))

                # 替换运输方式
                msg["detail"]["bookingOrderInfoBean"]["bookingBaseInfoBean"]["transportClause"] = \
                    msg["detail"]["bookingOrderInfoBean"]["bookingBaseInfoBean"]["transportClause"].replace("_", "-")
                # 将运行次数字段设置到数据中
                msg["count"] = count

                carrier_code = msg['detail']['carrierCode']  # 船公司

                # 校验redis，避免mq在订舱成功后重试
                name = f"booking-automatic-spider:{msg['detail']['carrierCode']}"
                isHad = RedisUtil().prevent_repeat(msg["messageId"], name)
                # 如果在redis查到mq的messageId并且不是失败重试的，暂停执行订舱
                if isHad and count == 1:
                    hgj_logger.warning(f"重试单子被拦截-{msg['messageId']}")
                    break

                if carrier_code in INTTRA_SUPPORT_CARRIER_LIST:
                    inttra = InttraSpider(msg, browser=browser, service=service)
                    inttra.main()
                    is_not_need_retry = inttra.is_not_need_retry
                    is_has_error_info = inttra.error_info
                    # 关闭浏览器
                    del inttra
                else:
                    # send_message_to_developer(f'{carrier_code} 不在支持的船公司中', [])
                    break

                if is_not_need_retry or not is_has_error_info:
                    break
                else:
                    count += 1
            except Exception as e:
                hgj_logger.error(e)
                carrier_code = msg['detail']['carrierCode']
                delegation_no = msg['detail']['delegationNo']

                # 删除文件
                send_message_to_developer(f"{carrier_code}-{delegation_no}订舱失败", [])
                count += 1


if __name__ == '__main__':
    mq = HandleMQ()
    mq.go()
    # with open("test.js", "r", encoding="utf-8") as f:
    #     message = f.read()
    # mess = json.loads(message)
    # de = mess["detail"]
    # mess["detail"] = json.loads(de)
    # mq.deal(mess)
