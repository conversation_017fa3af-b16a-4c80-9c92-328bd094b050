# -*- coding: utf-8 -*-
"""
工具类：爬虫
"""
import time
import base64
import json
import os
import cv2
from urllib.parse import quote

import requests
from hgj_loguru import hgj_logger
from retry.api import retry_call, retry
from selenium.webdriver.common.by import By

from utils.util_ffdm import FFDMApi
from utils.util_send_email import SendMail
from utils.util_rocketmq import ConnRocket
from utils import send_request, send_message_to_developer
from utils import create_authorization
from conf import NAS_URL, NAS_DOWNLOAD_URL, NAS_PREVIEW_URL, PROJECT_PATH, FFDM_CONFIG, MAIL_CONFIG, \
    MQ_CREATE_BILL_NO_TAG, MQ_UPDATE_BILL_NO_TAG, MQ_PRODUCER_ID, MQ_TOPIC, WEBSITE_INFO, RECORD_STATE


class Base:
    """扩展：制作视频，截图，推送"""
    task_name = ""
    # 睡眠时间 秒
    short_time = 0.2
    mid_time = 0.5
    long_time = 1

    def __init__(self):
        self.screen_flag = True                                                # 是否截屏
        self.logger = hgj_logger                                                # 创建logger
        self.util_mail = SendMail(**MAIL_CONFIG)                                # 创建mail对象
        self.verifycode_api = FFDMApi(**FFDM_CONFIG)                            # 初始化斐斐打码
        self.error_info, self.search_result_str, self.cur_bill_no = '', '', ''  # 初始化事件记录
        self.get_response = self.request
        self.is_not_need_retry = False                                          # 是否需要重试默认需要

    @staticmethod
    def request(url, method="get", **kwargs) -> requests.Response:
        """发送请求"""
        default_setting = {"method": method}
        default_setting.update(kwargs)
        response = retry_call(send_request, [url], default_setting, tries=1)
        return response

    def screen_browser(self, browser, delegation_no):
        """截图"""
        # 一定不存在，如果存在说明有地方错了
        screenshot_png_path = os.path.join(PROJECT_PATH, 'docs', f'{self.task_name}_{delegation_no}')
        if os.path.exists(screenshot_png_path):
            os.system(f'rm -rv {screenshot_png_path}')
        os.mkdir(screenshot_png_path)

        index = 1
        while True:
            if self.screen_flag is False:
                return True

            file_path = os.path.join(screenshot_png_path, f'{index}.png')
            retry_call(browser.save_screenshot, [file_path], tries=2)
            index += 1

    def make_video(self, delegation_no):
        """生成视频"""
        screenshot_png_path = os.path.join(PROJECT_PATH, 'docs', f'{self.task_name}_{delegation_no}')
        if not os.path.exists(screenshot_png_path) or not os.listdir(screenshot_png_path):
            return "", ""
        filelist = os.listdir(screenshot_png_path)
        filelist.sort(key=lambda x: int(x[:-4]))

        avi_path = os.path.join(PROJECT_PATH, 'docs', f"{self.task_name}_{delegation_no}.avi")
        mp4_path = os.path.join(PROJECT_PATH, 'docs', f"{self.task_name}_{delegation_no}.mp4")

        # 获取尺寸
        img = cv2.imread(os.path.join(screenshot_png_path, filelist[0]), 3)
        fps = 10  # 视频每秒24帧
        size = (img.shape[1], img.shape[0])  # 需要转为视频的图片的尺寸 (1280, 720)

        video = cv2.VideoWriter(avi_path, cv2.VideoWriter_fourcc('I', '4', '2', '0'), fps, size)
        # 视频保存在当前目录下
        for item in filelist:
            if item.endswith('.png'):
                # 找到路径中所有后缀名为.png的文件，可以更换为.jpg或其它
                item = os.path.join(screenshot_png_path, item)
                img = cv2.imread(item)
                video.write(img)
        video.release()

        # 压缩视频
        os.system(f'ffmpeg -i {avi_path} {mp4_path}')
        download_url, preview_url = self.upload_video(self.task_name, delegation_no)
        # 删除文件
        self.remove_useless_file(delegation_no=delegation_no)

        return download_url, preview_url

    def remove_useless_file(self, delegation_no):
        screenshot_png_path = os.path.join(PROJECT_PATH, 'docs', f'{self.task_name}_{delegation_no}')
        if not os.path.exists(screenshot_png_path): return
        avi_path = os.path.join(PROJECT_PATH, 'docs', f"{self.task_name}_{delegation_no}.avi")
        mp4_path = os.path.join(PROJECT_PATH, 'docs', f"{self.task_name}_{delegation_no}.mp4")
        # 删除文件夹
        os.path.exists(screenshot_png_path) and os.system(f'rm -rf {screenshot_png_path}')
        os.path.exists(avi_path) and os.system(f'rm {avi_path}')
        os.path.exists(mp4_path) and os.system(f'rm {mp4_path}')

    @retry(delay=1, tries=3)
    def upload_video(self, task_name, delegation_no):
        """上传mp4"""
        mp4_file_name = f"{task_name}_{delegation_no}.mp4"
        payload = {
            'type': 'booking-duckduckgo',
            'relevanceId': delegation_no,
            'downloadFileName': mp4_file_name
        }
        files = [
            ('multipartFile', (mp4_file_name, open(os.path.join(PROJECT_PATH, 'docs', mp4_file_name), 'rb'), 'application/octet-stream'))
        ]
        headers = {
            'appname': 'portArea_ebooking_booking',
            "authorization": create_authorization("video")
        }
        download_url, preview_url = '', ''
        try:
            resp_json = self.request(NAS_URL, method="post", headers=headers, data=payload, files=files).json()
        except Exception as e:
            self.logger.warning(e, exc_info=True)
            message = "".join([self.task_name, "视频上传失败！！！", "-"*30, "\n", str(e)])
            send_message_to_developer(message, [])
        else:
            data_id = resp_json.get('data', '')
            if data_id:
                download_url = NAS_DOWNLOAD_URL + data_id
                preview_url = NAS_PREVIEW_URL + quote(base64.b64encode(download_url.encode()))
        return download_url, preview_url

    def screen_picture(self, delegation_no, browser):
        try:
            screenshot_png_path = os.path.join(PROJECT_PATH, 'docs', "images")
            if not os.path.exists(screenshot_png_path): os.mkdir(screenshot_png_path)
            image_name = f'{self.task_name}_{delegation_no}.png'
            file_path = os.path.join(screenshot_png_path, image_name)
            browser.save_screenshot(file_path)
        except Exception as e:
            self.logger.info(e, exc_info=True)

    @retry(delay=1, tries=3)
    def upload_picture(self, delegation_no):
        """上传mp4"""
        download_url, preview_url = '', ''
        image_name = f"{self.task_name}_{delegation_no}.png"
        file_name = os.path.join(PROJECT_PATH, 'docs', "images", image_name)
        if os.path.exists(file_name):
            payload = {
                'type': 'booking-automatic-spider',
                'relevanceId': delegation_no,
                'downloadFileName': image_name
            }
            files = [
                ('multipartFile', (image_name, open(os.path.join(PROJECT_PATH, 'docs', "images", image_name), 'rb'), 'application/octet-stream'))
            ]
            headers = {
                'appname': 'booking-automatic-spider',
                "authorization": create_authorization("video")
            }
            try:
                resp_json = self.request(NAS_URL, method="post", headers=headers, data=payload, files=files).json()
            except Exception as e:
                self.logger.warning(e, exc_info=True)
                message = "".join([self.task_name, "图片上传失败！！！", "-"*30, "\n", str(e)])
                send_message_to_developer(message, [])
            else:
                data_id = resp_json.get('data', '')
                if data_id:
                    download_url = NAS_DOWNLOAD_URL + data_id
                    preview_url = NAS_PREVIEW_URL + quote(base64.b64encode(download_url.encode()))
            finally:
                os.path.exists(file_name) and os.system(f'rm {file_name}')
        return download_url, preview_url

    @retry(delay=1, tries=3)
    def send_result(self, msg, tag, flag, error_info, errors=None, draft_state=None, bill_no="", search_result="", is_have_video=False):
        """
        向后端传输数据
        :param msg: 详情数据
        :param tag: 区分回填的tag
        :param flag: 区分是否成功了
        :param error_info: 提示信息，不一定是错误信息
        :param errors: 提示信息，不一定是错误信息
        :param draft_state: 保存草稿状态信息
        :param bill_no: 订单号
        :param search_result: 出击结束查询的结果
        :param is_have_video: 是否需要制作视频
        :return:
        """
        # 获取数据参数
        record_id = msg.get("recordId", "")
        booking_id = msg["bookingId"]
        count = msg.get("count", 1)

        if count == 2 and not flag:
            error_info = "".join([error_info, "  连续上传错误俩次"])
        message = {"bookingId": booking_id, "billNo": bill_no, "errorInfo": error_info, "flag": flag}
        mq_tag = "booking"
        download_url, preview_url = None, None
        # 区分tag
        if tag == 1:
            carrier_code = msg["detail"]["carrierCode"]
            delegation_no = msg["detail"]["delegationNo"]
            if RECORD_STATE:
                # 上传视频
                download_url, preview_url = self.make_video(delegation_no=delegation_no)
            else:
                # 上传图片
                download_url, preview_url = self.upload_picture(delegation_no=delegation_no)
            message.update({"recordId": record_id, "videoDownloadUrl": error_info and download_url, "videoPreviewUrl": error_info and preview_url, "searchResult": search_result})
            # 保存草稿处理
            message.update({"delegationNo": delegation_no, "carrierCode": carrier_code, "draftState": draft_state, "website": WEBSITE_INFO[self.task_name]["website"], "websiteName": WEBSITE_INFO[self.task_name]["website_name"]})
            errors and message.update({"errors": errors or []})
            mq_tag = MQ_CREATE_BILL_NO_TAG
        elif tag == 2:
            mq_tag = MQ_UPDATE_BILL_NO_TAG

        # todo 记得删除
        self.logger.info(f"{message}-{mq_tag}")
        conn = ConnRocket()
        conn.produce_message(MQ_PRODUCER_ID, MQ_TOPIC, json.dumps(message), tags=mq_tag, arg=int(time.time()))
        # 后面追加
        all([download_url, preview_url]) and message.update({"videoDownloadUrl": download_url, "videoPreviewUrl": preview_url})
        return message

    @staticmethod
    def parse_relation_info(task):
        """
        提取收发通信息
        :param task: 得到的订舱信息数据
        :return: 返回重新包装的收发通信息
        """
        relation_dict = {}
        for one in ["shipper", "consignee", "notify", "secondNotify"]:
            detail = task[f"{one}ContactInfoBean"]
            relation_dict[one] = dict(map(lambda x: (x[0].split(one)[1].lower(), x[1]), detail.items()))
        return relation_dict

    @staticmethod
    def parse_container_info(msg):
        """梳理预配箱数据"""
        container_prefect_info = []
        container_types = msg["containerTypes"]
        container_info_params_dict = dict(map(lambda x: (x["containerCode"], x), container_types))
        container_compare_list = msg["detail"]["bookingOrderInfoBean"]["containerInfoBeanList"]
        for one in container_compare_list:
            one.update(container_info_params_dict[one["containerType"]])
            container_prefect_info.append(one)
        return container_prefect_info

    def clear_and_send_keys(self, browser, by: str, rule: str, msg):
        """
        点击并发送
        每次都获取是因为有时旧的元素定位不到会报错，所以每次都重新获取元素并完成相应操作
        :param browser: 浏览器对象
        :param by: 通过什么方式定位，如： id，xpath，css_selector
        :param rule: 定位的规则
        :param msg: 需要输入的值
        :return:
        """
        by = by.lower()
        self.__get_element(browser, by, rule).clear()
        time.sleep(0.1)
        self.__get_element(browser, by, rule).send_keys(msg)
        time.sleep(0.2)

    def click_by_js(self, browser, by, rule):
        """通过js点击"""
        by = by.lower()
        ele = self.__get_element(browser, by, rule)
        browser.execute_script("arguments[0].click();", ele)
        time.sleep(0.2)

    def scroll_to_ele(self, browser, by, rule):
        """滚动到某个元素的位置"""
        ele = self.__get_element(browser, by, rule)
        js4 = "arguments[0].scrollIntoView();"
        browser.execute_script(js4, ele)
        return True

    def __get_element(self, browser, by, value):
        """通过不同的方式查找界面元素"""
        element = None
        by = by.lower()
        if hasattr(By, by.upper()):
            element = browser.find_element(by=by, value=value)
        else:
            self.logger.error("无对应方法，请检查")
        return element

