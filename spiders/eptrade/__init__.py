from tenacity import retry, stop_after_attempt, wait_fixed

from conf import GET_COOKIE_URL
from spiders.eptrade.config import GET_COOKIE_KEY, GET_COOKIE_USERNAME
from utils import send_request, create_authorization
from utils.util_retry import return_after_retry


class BaseEptradeSpider:
    def __init__(self):
        self.cookies = self.get_cookie(key=GET_COOKIE_KEY, username=GET_COOKIE_USERNAME)

    @retry(stop=stop_after_attempt(2), wait=wait_fixed(5), retry_error_callback=return_after_retry)
    def get_cookie(self, key, username):
        """获取cookie"""
        form_data = {"username": username}
        params = {"key": key}
        resp = send_request(url=GET_COOKIE_URL, method="post", data=form_data, params=params, topic="json")
        resp.raise_for_status()
        return resp.json()["data"]["cookies"]

    @staticmethod
    @retry(stop=stop_after_attempt(3), wait=wait_fixed(5), retry_error_callback=return_after_retry)
    def upload_result(url, data):
        """向订舱后端请求数据"""
        headers = {"authorization": create_authorization("booking")}
        kw = {"headers": headers, "topic": "json", "timeout": 10, "data": data}
        response = send_request(url, method="post", **kw)
        response.raise_for_status()
        return response