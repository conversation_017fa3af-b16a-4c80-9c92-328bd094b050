"""
亿通绑定车队信息 14：00
"""
import json
import os
from threading import Thread

from tenacity import retry, stop_after_attempt, wait_fixed

from server import app
from spiders.eptrade import BaseEptradeSpider
from spiders.eptrade.config import MQ_SUBSCRIBE_TAG_TEAM, UPLOAD_EPTRADE_API, CONTACTS
from utils import send_message_to_developer, send_request, create_authorization
from utils.util_retry import return_after_retry
from utils.util_rocketmq import ConnRocket
from conf import MQ_SUBSCRIBE_ID, GET_COOKIE_URL, MQ_TOPIC

# 记录日志
file_name = os.path.basename(__file__)
logger = app.logger


class BindTeam(BaseEptradeSpider):
    def __init__(self, msg):
        super().__init__()
        self.msg = msg
        self.team_url = ""
        self.bill_no = self.msg.get("bill_no").replace("ONEY","")
        self.team = self.msg.get("team")
        self.error_msg = None

    def main(self):
        if not self.cookies:
            logger.warning("获取cookie失败")
            self.error_msg = "登录异常，获取cookie失败"
        team_data = {}
        team_params = {}
        team_headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36", }
        response = send_request(url=self.team_url, method="post", headers=team_headers, cookies=self.cookies["cookies"],
                                params=team_params, data=team_data)
        if response.status_code != 200:
            logger.warning(f"查询失败,返回状态码异常,状态码：{response.status_code},返回内容：{response.content}")
            send_message_to_developer(f"{file_name}-查询失败,返回状态码异常,请检查日志", CONTACTS)
        elif response.json()["total"] == 0:
            logger.info(f"未查询到该订舱信息,订舱单号：{self.bill_no}")
            send_message_to_developer(f"{file_name}-未查询到该订舱信息,订舱单号：{self.bill_no}", CONTACTS)
        elif response.json()["total"] > 1:
            logger.warning("查询到多个订舱信息,请检查数据")
            send_message_to_developer(f"{file_name}-查询到多个订舱信息,请检查数据", CONTACTS)
        else:
            data = response.json()["rows"][0]
            self.handle_data(self.msg, data)

    def handle_data(self, org_msg, data):
        """处理返回数据"""
        if data.get("eirResData"):
            form_data = {
                "pythonOperateType": "BIND_MOTORCADE_RESULT_NOTIFY",
                "bookingId": org_msg.get("bookingId"),
                "carrierCode": org_msg.get("carrierCode"),
                "billNo": org_msg.get("billNo"),
                "successFlag": True,
                "errorInfo": self.error_msg
            }
            self.upload_result(url=UPLOAD_EPTRADE_API,data=form_data)
        if self.error_msg:
            send_message_to_developer(f"{file_name}-绑定车队失败,原因：{self.error_msg}", CONTACTS)

    @retry(stop=stop_after_attempt(2), wait=wait_fixed(5), retry_error_callback=return_after_retry)
    def get_cookie(self, key, username):
        """获取cookie"""
        form_data = {"username": username}
        params = {"key": key}
        resp = send_request(url=GET_COOKIE_URL, method="post", data=form_data, params=params, topic="json")
        resp.raise_for_status()
        return resp.json()["data"]["cookies"]

    @staticmethod
    @retry(stop=stop_after_attempt(3), wait=wait_fixed(5), retry_error_callback=return_after_retry)
    def upload_result(url, data):
        """向订舱后端请求数据"""
        headers = {"authorization": create_authorization("booking")}
        kw = {"headers": headers, "topic": "json", "timeout": 10, "data": data}
        response = send_request(url, method="post", **kw)
        response.raise_for_status()
        return response

class HandleMQ(object):
    """处理消息队列"""

    def __init__(self):
        self.rocket = ConnRocket()
        self.logger = app.logger

    def go(self):
        """启动消息队列消费"""
        self.rocket.push_message(
            group_id=MQ_SUBSCRIBE_ID,
            topic=MQ_TOPIC,
            func=self.deal,
            expression=MQ_SUBSCRIBE_TAG_TEAM
        )

    def deal(self, msg):
        """处理消息队列消息"""
        try:
            msg_body = json.loads(msg.body)
            self.logger.info(f"当前待处理消息ID:{msg.id}")
            bind_team = BindTeam(msg_body)
            bind_team.main()
            self.logger.info(f"消息处理结束:{msg.id}")
        except Exception as e:
            self.logger.error(f"消息处理异常，异常信息: {str(e)}", exc_info=True)
            send_message_to_developer(f"{file_name}-消息处理异常，请查看日志", CONTACTS)


if __name__ == '__main__':
    Thread(target=HandleMQ().go).start()
