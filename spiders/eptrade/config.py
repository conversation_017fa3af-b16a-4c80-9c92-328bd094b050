CONTACTS = ['***********']
GET_COOKIE_USERNAME = "CN341827"
GET_COOKIE_KEY = "eptrade"
# 箱管回复和绑定车队结果通知接口
UPLOAD_EPTRADE_API = "https://dev-apisix.hgj.com/booking-common-channel/doc.html#/Aksk%E9%89%B4%E6%9D%83/aksk%E9%89%B4%E6%9D%83-python%E7%9B%B8%E5%85%B3%E6%93%8D%E4%BD%9C%E7%9A%84%E5%85%A5%E5%8F%A3/pythonOperateHandle"

MQ_SUBSCRIBE_TAG_TEAM = "booking-first-bc-bind-motorcade-tag" # 绑定车队消息tag

QUERY_PARAMS = {
    "businessPortCodes": ["SHANGHAI"],
    "carrierCodes": ["ONE"],
    "bookingStatuses": ["SUBMITTED_FOR_HGJ_REVIEW",
                        "SENT_TO_SHIPPING_COMPANY_FOR_RELEASE",
                        "SHIPPING_COMPANY_RELEASE"],
    "supplementStatuses": ["NO_SUBMITTED", "REJECTED", "SENT_TO_SHIPPING_COMPANY"]
}
BASE_HEADERS = {
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Connection": "keep-alive",
    "Sec-Fetch-Site": "same-origin",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\""
}
