# -*- coding: utf-8 -*-
import os
import logging
from logging import LogRecord

from gunicorn.glogging import Logger
from hgj_loguru import hgj_logger, custom_logger

from conf import PROJECT_PATH


class MessageFilter(logging.Filter):
    """过滤冗余请求"""
    def filter(self, record: LogRecord) -> int:
        msg = record.getMessage()
        if msg.find("/actuator/health") != -1 or msg.find("/actuator/prometheus") != -1 or msg.find("/favicon") != -1:
            return False
        return True


hgj_logger.add_filter(MessageFilter())

gunicorn_error_logger = custom_logger.get_custom_logger("gunicorn.error", os.path.join(PROJECT_PATH, "logs", "gunicorn.error.log"), level=logging.INFO)


class GunicornLogger(Logger):

    def __init__(self, cfg=None):
        self.cfg = cfg
        self.error_log = gunicorn_error_logger
        self.access_log = hgj_logger
        self.error_log.level = logging.INFO
        self.access_log.level = logging.INFO

