import time
from selenium.webdriver.common.by import By
import undetected_chromedriver as uc
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

driver = uc.Chrome(
    driver_executable_path=r"C:\Users\<USER>\.wdm\drivers\chromedriver\win64\137.0.7151.104\chromedriver-win64\chromedriver.exe")
driver.get('https://passport.wanhai.com/cas/login')
WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
# driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
#             "source": """
#                     delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
#                     delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
#                     delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
#                     Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
#                 """
#         })
driver.find_element(By.XPATH, "//a[@title='账号登录']").click()
time.sleep(2)
driver.find_element(By.ID, "companyCode").send_keys("D735")
driver.find_element(By.ID, "username").send_keys("D735")
driver.find_element(By.ID, "password").send_keys("SqVK12rY")
captcha_code = input("手动输入验证码:")
driver.find_element(By.ID, "captchaCode").send_keys(captcha_code)
driver.find_element(By.XPATH, "//form[@id='fm1']/input[@class='btn btn-block btn-submit']").submit()
time.sleep(20)
