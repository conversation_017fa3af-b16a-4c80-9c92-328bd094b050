# -*- coding: utf-8 -*-
"""
根据在cma lara查询到的信息发送拒绝数据
"""
import time

from hgj_loguru import hgj_logger as logger

from conf import CANCEL_API
from utils.util_normal import send_request
from utils.util_create_authorization import create_authorization


def handle_result(payload):
    url = CANCEL_API
    headers = {
        "authorization": create_authorization("booking")
    }
    response = send_request(method="post", url=url, data=payload, headers=headers, topic="json", mask=True)
    return response


def send_error(origin_data, reason, is_need_wait=False):
    """处理发送取消数据"""
    cancel_list = ["取消"]
    if list(filter(lambda x: x.lower() in reason.lower(), cancel_list)):
        # 防止未更新提单就拒绝
        if is_need_wait: time.sleep(2)
        origin_data.update({"rejectedReason": reason[:256], "executionTime": time.strftime("%Y-%m-%d %H:%M:%S"), "source": "WEB"})
        response = handle_result(origin_data)
        logger.info(f"{origin_data['billNo']}-{response.text}")
