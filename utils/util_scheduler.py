# -*- coding: utf-8 -*-
"""
@Author: 汤宪昆
@Date: 2021-12-21
@Description:
    1. 通过nohup进行启动
    2. 使用kill杀死查询到的进程
"""

import os
import signal
import time


class Scheduler(object):

    def start(self, name, file_path) -> bool:
        """
        开启程序
        :param name: 启动的文件名称
        :param file_path: 需要启动的文件夹
        :return: 启动成功: True, 失败: False
        """
        for i in range(3):
            process_info = os.popen(f'ps -ef | grep {name} | grep -v grep').readlines()
            process_num = len(process_info)
            if i == 0 and process_num:      # 如果已经有运行的进行进行杀死重启
                stop_res = self.stop(name=name)
                if not stop_res:
                    return stop_res
            if not process_num:
                os.system(f'nohup python3 {file_path} 1>/dev/null 2>&1 &')
                time.sleep(1)
            else:
                return True
        else:
            return False

    @staticmethod
    def stop(name) -> bool:
        """
        停止程序
        @:param name: 停止的程序名称
        :return: 启动成功: True, 失败: False
        """
        for i in range(3):
            process_info = os.popen(f"ps -ef|grep {name} |grep -v " + "grep|awk '{print $2}'").readlines()
            for pid in process_info:
                os.kill(int(pid), signal.SIGKILL)

            if len(os.popen(f"ps -ef|grep {name} |grep -v " + "grep|awk '{print $2}'").readlines()):
                time.sleep(2)
            else:
                return True
        else:
            return False

