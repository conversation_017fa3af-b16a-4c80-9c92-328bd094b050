from tenacity import retry, stop_after_attempt, wait_fixed

from conf import GET_PROXY_URL
from utils import send_request
from utils.util_retry import return_after_retry


@retry(stop=stop_after_attempt(2), wait=wait_fixed(1), retry_error_callback=return_after_retry)
def get_proxy(proxy_name):
    """获取代理"""
    params = {"proxy_name": proxy_name, "count": 1}
    resp = send_request(url=GET_PROXY_URL, method="get", params=params, topic="json")
    resp.raise_for_status()
    auth = resp.json()["data"][0]["auth"]
    proxy = resp.json()["data"][0]["ip"]
    return {
        "http": f"http://{auth}@{proxy}",
        "https": f"http://{auth}@{proxy}"
    }
