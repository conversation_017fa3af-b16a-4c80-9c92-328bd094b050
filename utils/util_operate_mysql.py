# -*- coding: utf-8 -*-
"""
操作数据库
"""
from typing import Union, Tuple

import pymysql
from dbutils.pooled_db import PooledDB


class MysqlSpiderUtil(object):
    _instance = None
    __pool = None

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = object.__new__(cls)
        return cls._instance

    def __init__(self, *args, **kwargs):
        self.host = kwargs.get("host")
        self.port = kwargs.get("port")
        self.user = kwargs.get("user")
        self.password = kwargs.get("password")
        self.db = kwargs.get("db")
        self.charset = kwargs.get("charset")

    def __create_conn(self):
        if self.__pool is None:
            # 如果连接池中没有可用连接，设置blocking=True，阻塞等待
            self.__pool = PooledDB(pymysql, maxcached=3, maxshared=5, maxconnections=5, blocking=True,
                                   host=self.host, user=self.user, password=self.password,
                                   database=self.db, port=self.port, charset=self.charset
                                   )

        return self.__pool.connection()

    def __get_conn(self):
        conn = self.__create_conn()
        cursor = conn.cursor()
        return conn, cursor

    @staticmethod
    def __release_conn(conn, cursor):
        conn and conn.close()
        cursor and cursor.close()

    def get_all(self, sql: str, params: Tuple[Union[int, float, str], ...] = None):
        conn, cursor = None, None
        try:
            conn, cursor = self.__get_conn()
            count = cursor.execute(sql, params)
            if count > 0:
                result = cursor.fetchall()
            else:
                result = ()
            return result
        finally:
            self.__release_conn(conn, cursor)

    def get_one(self, sql: str, params: Tuple[Union[int, float, str], ...] = None) -> tuple:
        conn, cursor = None, None
        try:
            conn, cursor = self.__get_conn()
            count = cursor.execute(sql, params)
            if count > 0:
                result = cursor.fetchone()
            else:
                result = ()
            self.__release_conn(conn, cursor)
            return result
        finally:
            self.__release_conn(conn, cursor)

    def update(self, sql: str, params: Tuple[Union[int, float, str], ...] = None):
        conn, cursor = None, None
        try:
            conn, cursor = self.__get_conn()
            count = cursor.execute(sql, params)
            conn.commit()
            primary_key_id = cursor.lastrowid
            self.__release_conn(conn, cursor)
            return count, primary_key_id
        finally:
            self.__release_conn(conn, cursor)

    def __del__(self):
        self.__pool and self.__pool.close()
