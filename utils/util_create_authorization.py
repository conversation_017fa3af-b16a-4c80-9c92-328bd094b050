# -*- coding: utf-8 -*-
import time
import uuid
import base64
from hashlib import sha256
import hmac

from conf import AKSK


def create_authorization(aksk_type):
    """生成authorization"""
    aksk = AKSK.get(aksk_type)
    access_key, secret_key = aksk["ACCESS_KEY"], aksk["SECRET_KEY"]
    uuid_value = str(uuid.uuid4())
    uuid_value = uuid_value.replace("-", "")
    nonce = uuid_value[:10]
    timestamp = int(time.time())
    # 加密的字符串
    data = "".join(map(lambda i: str(i), [access_key, nonce, timestamp]))
    # 使用hmac加密
    hmac_str = hmac.new(secret_key.encode(), data.encode(), digestmod=sha256).hexdigest()
    # 拼接base64加密字符串
    raw_data = "/".join(map(lambda i: str(i), [access_key, nonce, timestamp, hmac_str]))
    # base64转码
    bs4_res = base64.encodebytes(raw_data.encode())
    # 将二进制转为字符串,替换换行符
    result = bs4_res.decode().replace("\n", "")
    return result


if __name__ == '__main__':
    res = create_authorization("booking")
    print(res)
