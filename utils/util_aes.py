# -*- coding: utf-8 -*-
from Crypto.Cipher import AES
from Crypto.Hash import MD5
from Crypto.Util.Padding import unpad


def aes_ecb_decrypt(params, value) -> str:
    # 生成密钥
    key_source = f"{params[0]}:{params[1]}"
    password_encrypted = value
    key = MD5.new(key_source.encode('utf-8')).hexdigest()[8:-8]
    cipher = AES.new(key.encode('utf-8'), AES.MODE_ECB)
    padded_password = cipher.decrypt(bytes.fromhex(password_encrypted))
    password = unpad(padded_password, AES.block_size).decode()
    return password

