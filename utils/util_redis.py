# -*- coding: utf-8 -*-
"""
操作数据库
"""
import redis

from conf import REDIS_CONFIG


class RedisUtil(object):
    _instance = None
    __pool = None

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = object.__new__(cls)
        return cls._instance

    def __init__(self, **kwargs):
        REDIS_CONFIG.update(kwargs)
        self.conn = self.create_conn(**REDIS_CONFIG)

    def create_conn(self, **kwargs):
        if self.__pool is None:
            self.__pool = redis.ConnectionPool(**kwargs)
        conn = redis.Redis(connection_pool=self.__pool, decode_responses=True)
        return conn

    @classmethod
    def prevent_repeat(cls, message_id, name):
        """
        使用redis消除mq的多余重试
        :param message_id: mq的唯一标识
        :param name: 保存到redis的键名称
        :return: 如果已经存在就返回True
        """
        conn = cls().create_conn()
        values = conn.lrange(name, 0, -1)
        if message_id.encode() in values:
            return True
        len(values) > 100 and conn.rpop(name)
        conn.lpush(name, message_id)


if __name__ == '__main__':
    r = RedisUtil()
    conn = r.conn
    # res = conn.keys("booking-automatic-spider:EMC")
    # res = conn.lpush("booking-automatic-spider:EMC", b"4A0410AC00008F55000093152960D200")
    res = conn.lrange("booking-automatic-spider:EMC", 0, -1)
    # res = conn.lpop("booking-automatic-spider:EMC")
    print(res)
