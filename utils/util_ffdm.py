# -*- coding: utf-8 -*-
"""
斐斐打码平台
"""

import hashlib
import time
import json
import requests

from utils import send_message_to_developer
from retry.api import retry


BASE_URL = "http://pred.fateadm.com"


class Rsp:
    """处理响应数据"""
    def __init__(self):
        self.ret_code = -1
        self.cust_val = 0.0
        self.err_msg = "succ"
        self.value = ""
        self.request_id = ""

    def parse_json_rsp(self, rsp_data):
        if rsp_data is None:
            self.err_msg = "http request failed, get rsp Nil data"
            return
        jrsp = json.loads(rsp_data)
        self.ret_code = int(jrsp["RetCode"])
        self.err_msg = jrsp["ErrMsg"]
        self.request_id = jrsp["RequestId"]
        if self.ret_code == 0:
            rslt_data = jrsp["RspData"]
            if rslt_data is not None and rslt_data != "":
                jrsp_ext = json.loads(rslt_data)
                if "cust_val" in jrsp_ext:
                    data = jrsp_ext["cust_val"]
                    self.cust_val = float(data)
                if "result" in jrsp_ext:
                    data = jrsp_ext["result"]
                    self.value = data


def generate_sign(pd_id, passwd, timestamp):
    """生成sign"""
    md5 = hashlib.md5()
    md5.update((timestamp + passwd).encode())
    csign = md5.hexdigest()

    md5 = hashlib.md5()
    md5.update((pd_id + timestamp + csign).encode())
    csign = md5.hexdigest()
    return csign


def calc_card_sign(cardid, cardkey, timestamp, passwd):
    """生成充值sign"""
    md5 = hashlib.md5()
    md5.update(passwd + timestamp + cardid + cardkey)
    return md5.hexdigest()


def get_response(url, body_data, img_data=""):
    """获取响应"""
    rsp = Rsp()
    files = {
        'img_data': ('img_data', img_data)
    }
    rsp_data = requests.post(url, body_data, files=files)
    rsp.parse_json_rsp(rsp_data.text)
    return rsp


class FFDMApi:
    """斐斐打码平台接口"""
    _instance = None

    def __new__(cls, *args, **kwargs):
        """使用单例实例化"""
        if not cls._instance:
            cls._instance = object.__new__(cls)
            return cls._instance
        return cls._instance

    def __init__(self, app_id, app_key, pd_id, pd_key):
        """初始化实例"""
        self.app_id = app_id
        self.app_key = app_key
        self.pd_id = pd_id
        self.pd_key = pd_key
        self.host = BASE_URL

    @retry(tries=2, exceptions=KeyError)
    def query_balance(self):
        """
        查询余额
        :return:
            rsp.ret_code：正常返回0
            rsp.cust_val：用户余额
            rsp.err_msg：异常时返回异常详情
        """
        timestamp = str(int(time.time()))
        sign = generate_sign(self.pd_id, self.pd_key, timestamp)
        param = {
            "user_id": self.pd_id,
            "timestamp": timestamp,
            "sign": sign
        }
        url = self.host + "/api/custval"
        rsp = get_response(url, param)
        if rsp.ret_code != 0:
            raise KeyError("查询失败")
        if rsp.cust_val < 500:
            send_message_to_developer(message="斐斐打码余额不足", contact=[])
        return rsp

    def query_tts(self, pred_type):
        """
        查询网络延迟
        :param pred_type: 识别类型
        :return:
            rsp.ret_code：正常返回0
            rsp.err_msg： 异常时返回异常详情
        """
        tm = str(int(time.time()))
        sign = generate_sign(self.pd_id, self.pd_key, tm)
        param = {
            "user_id": self.pd_id,
            "timestamp": tm,
            "sign": sign,
            "predict_type": pred_type,
        }
        if self.app_id != "":
            asign = generate_sign(self.app_id, self.app_key, tm)
            param["appid"] = self.app_id
            param["asign"] = asign
        url = self.host + "/api/qcrtt"
        rsp = get_response(url, param)
        if rsp.ret_code == 0:
            print("query rtt succ ret: {} request_id: {} err: {}".format(rsp.ret_code, rsp.request_id, rsp.err_msg))
        else:
            print("predict failed ret: {} err: {}".format(rsp.ret_code, rsp.err_msg.encode('utf-8')))
        return rsp

    def predict(self, pred_type, img_data, src_url=""):
        """
        识别验证码
        :param pred_type: 识别类型
        :param img_data: 图片数据
        :param src_url: img_data的源网站地址
        :return:
            rsp.ret_code：正常返回0
            rsp.request_id：唯一订单号
            rsp.value：识别结果
            rsp.err_msg：异常时返回异常详情
        """
        tm = str(int(time.time()))
        sign = generate_sign(self.pd_id, self.pd_key, tm)
        param = {
            "user_id": self.pd_id,
            "timestamp": tm,
            "sign": sign,
            "predict_type": pred_type,
            "up_type": "mt"
        }
        if src_url is not None or src_url != "":
            param["src_url"] = src_url
        if self.app_id != "":
            asign = generate_sign(self.app_id, self.app_key, tm)
            param["appid"] = self.app_id
            param["asign"] = asign
        url = self.host + "/api/capreg"
        files = img_data
        rsp = get_response(url, param, files)
        if rsp.ret_code == 4003:
            self.justice(request_id=rsp.request_id)
        return rsp

    def predict_from_file(self, pred_type, file_name):
        """
        从文件进行验证码识别
        :param pred_type: 识别类型
        :param file_name: 文件名
        :return:
            rsp.ret_code：正常返回0
            rsp.request_id：唯一订单号
            rsp.value：识别结果
            rsp.err_msg：异常时返回异常详情
        """
        with open(file_name, "rb") as f:
            data = f.read()
        return self.predict(pred_type, data)

    @retry(tries=2)
    def justice(self, request_id):
        """
        识别失败，进行退款请求
        :param request_id: 需要退款的订单号
        :return: 成功 True, 失败 False
        """
        if request_id == "":
            return False
        tm = str(int(time.time()))
        sign = generate_sign(self.pd_id, self.pd_key, tm)
        param = {
            "user_id": self.pd_id,
            "timestamp": tm,
            "sign": sign,
            "request_id": request_id
        }
        url = self.host + "/api/capjust"
        rsp = get_response(url, param)
        if rsp.ret_code == 0:
            return True
        else:
            return False

    def charge(self, cardid, cardkey):
        """
        充值接口
        :param cardid: 充值卡号
        :param cardkey: 充值卡签名串
        :return:
            rsp.ret_code：正常返回0
            rsp.err_msg：异常时返回异常详情
        """
        tm = str(int(time.time()))
        sign = generate_sign(self.pd_id, self.pd_key, tm)
        csign = calc_card_sign(cardid, cardkey, tm, self.pd_key)
        param = {
            "user_id": self.pd_id,
            "timestamp": tm,
            "sign": sign,
            'cardid': cardid,
            'csign': csign
        }
        url = self.host + "/api/charge"
        rsp = get_response(url, param)
        return rsp

    def extend_charge(self, cardid, cardkey):
        """
        充值，只返回是否成功
        :param cardid: 充值卡号
        :param cardkey: 充值卡签名串
        :return: 充值成功时返回0
        """
        return self.charge(cardid, cardkey).ret_code

    def justice_extend(self, request_id):
        """
        调用退款，只返回是否成功
        :param request_id: 需要退款的订单号
        :return: 退款成功时返回0
        """
        return self.justice(request_id).ret_code

    def query_balance_extend(self):
        """
        查询余额，只返回余额
        :return: 余额
        """
        rsp = self.query_balance()
        return rsp.cust_val

    def predict_from_file_extend(self, pred_type, file_name):
        """
        从文件识别验证码，只返回识别结果
        :param pred_type: 识别类型
        :param file_name: 文件名
        :return: 识别的结果
        """
        rsp = self.predict_from_file(pred_type, file_name)
        return rsp.value

    def predict_extend(self, pred_type, img_data):
        """
        识别接口，只返回识别结果
        :param pred_type: 识别类型
        :param img_data: 图片的数据
        :return: 识别的结果
        """
        rsp = self.predict(pred_type, img_data)
        return rsp.value

    def predict_extend_code_id(self, pred_type, img_data, src_url=""):
        """
        识别接口，只返回识别结果
        :param pred_type: 识别类型
        :param img_data: 图片的数据
        :param src_url: img_data图片数据来源
        :return:
            rsp.request_id：唯一订单号
            rsp.value：识别结果
        """
        rsp = self.predict(pred_type, img_data, src_url=src_url)
        return rsp.value, rsp.request_id

