# -*- coding: utf-8 -*-
"""
根据undetected_chromedriver进行优化的版本
"""
import io
import os.path
import re
import sys
import time
import psutil
import shutil
import tempfile
import subprocess

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common import service as webdriver_service

from conf import PLUGIN_JS
from utils.util_system_info import OperationSystemManagerUtil

WIDTH = 1792
HEIGHT = 1120
IS_POSIX = sys.platform.startswith(("darwin", "cygwin", "linux", "linux2"))
CHROME_NAME = ("google-chrome", "chromium", "chromium-browser", "chrome", "google-chrome-stable")


class SeleniumBaseSpider:

    _instance = None
    browser = None
    service = None
    create_time = 0

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = object.__new__(cls)
        return cls._instance

    def __init__(self, logger, *args, **kwargs):
        self.headless = True                                                                # 无头模式
        self.userdata_file_path = None                                                      # 浏览器目录
        self.page_load_timeout = 30                                                         # 秒
        # 是否使用manager管理driver
        self.chromedriver = "/usr/local/bin/chromedriver"
        self.logger = logger
        self.browser_id = 0
        self.chrome_version = self.get_webdriver_version()

    @staticmethod
    def get_webdriver_version():
        """获取webdriver版本, 只兼容chrome浏览器"""
        return int(OperationSystemManagerUtil().get_browser_version_from_os("google-chrome").split(".", maxsplit=1)[0])

    def add_argument(self, options):
        # todo如果多账号时记得修改user-data-dir
        argument_list = ['--no-sandbox', '--disable-gpu', '--disable-infobars', '--disable-dev-shm-usage', '--lang=zh-CN', '--disable-infobars'
                         '--ignore-certificate-errors', '--no-default-browser-check', '--no-first-run', '--incognito', '--test-type', '--log-level=0',
                         '--blink-settings=imagesEnabled=false', '--disable-javascript']
        options.arguments.extend(argument_list)
        if self.headless:
            options.add_argument("--headless=new") if self.chrome_version > 108 else options.add_argument("--headless=chrome")
        self.userdata_file_path = os.path.normpath(tempfile.mkdtemp())
        options.add_argument(f"--user-data-dir={self.userdata_file_path}")

    def patch_exe(self):
        """修改webdriver 标记"""
        start = time.perf_counter()
        with io.open(self.chromedriver, "r+b") as fh:
            content = fh.read()
            # match_injected_codeblock = re.search(rb"{window.*;}", content)
            match_injected_codeblock = re.search(b"\\{window\\.cdc.*?;}", content)
            if match_injected_codeblock:
                target_bytes = match_injected_codeblock[0]
                new_target_bytes = (
                    b'{console.log("undetected chromedriver 1337!")}'.ljust(
                        len(target_bytes), b" "
                    )
                )
                new_content = content.replace(target_bytes, new_target_bytes)
                if new_content == content:
                    self.logger.warning(
                        "something went wrong patching the driver binary. could not find injection code block"
                    )
                else:
                    self.logger.debug(
                        "found block:\n%s\nreplacing with:\n%s"
                        % (target_bytes, new_target_bytes)
                    )
                fh.seek(0)
                fh.write(new_content)
        self.logger.debug(
            "patching took us {:.2f} seconds".format(time.perf_counter() - start)
        )

    def find_chrome_executable(self):
        """
        Finds the chrome, chrome beta, chrome canary, chromium executable

        Returns
        -------
        executable_path :  str
            the full file path to found executable

        """
        candidates = set()
        if IS_POSIX:
            for item in os.environ.get("PATH").split(os.pathsep):
                for subitem in CHROME_NAME:
                    candidates.add(os.sep.join((item, subitem)))
            if "darwin" in sys.platform:
                candidates.update(
                    [
                        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                        "/Applications/Chromium.app/Contents/MacOS/Chromium",
                    ]
                )
        else:
            for item in map(
                    os.environ.get,
                    ("PROGRAMFILES", "PROGRAMFILES(X86)", "LOCALAPPDATA", "PROGRAMW6432"),
            ):
                if item is not None:
                    for subitem in (
                            "Google/Chrome/Application",
                    ):
                        candidates.add(os.sep.join((item, subitem, "chrome.exe")))
        for candidate in candidates:
            self.logger.debug('checking if %s exists and is executable' % candidate)
            if os.path.exists(candidate) and os.access(candidate, os.X_OK):
                self.logger.debug('found! using %s' % candidate)
                return os.path.normpath(candidate)

    def start_chrome(self, options):
        if not options.debugger_address:
            debug_port = webdriver_service.utils.free_port()
            debug_host = "127.0.0.1"
            options.debugger_address = "%s:%d" % (debug_host, debug_port)
        else:
            debug_host, debug_port = options.debugger_address.split(":")
            debug_port = int(debug_port)

        options.set_capability(
            "goog:loggingPrefs", {"performance": "ALL", "browser": "ALL"}
        )

        options.add_argument("--remote-debugging-host=%s" % debug_host)
        options.add_argument("--remote-debugging-port=%s" % debug_port)
        options.binary_location = self.find_chrome_executable()
        browser = subprocess.Popen(
            [options.binary_location, *options.arguments],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            close_fds=IS_POSIX,
        )
        self.browser_id = browser.pid
        return options

    def create_browser(self):
        """
        初始化一个chrome
        """
        if self.browser and self.service:
            try:
                self.browser.execute_script("javascript:void(0);")
                assert time.time() - 12*60*60 < self.create_time
            except:
                self.release()
            else:
                return self.browser, self.service
        chrome_options = webdriver.ChromeOptions()
        self.add_argument(chrome_options)
        self.patch_exe()
        self.start_chrome(chrome_options)
        for i in range(2):
            try:
                service = Service(executable_path=self.chromedriver)
                service.command_line_args()
                browser = webdriver.Chrome(service=service, options=chrome_options)
                # 保存当前的时间戳
                self.create_time = time.time()
                break
            except Exception as e:
                self.logger.error(e, exc_info=True)
        else:
            # 浏览器启动失败
            raise

        browser.set_page_load_timeout(self.page_load_timeout)
        browser.set_window_size(width=WIDTH, height=HEIGHT)
        js = open(PLUGIN_JS, "r").read()
        browser.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {'source': js})
        self.browser, self.service = browser, service
        return browser, service

    def close_browser(self, service, browser):
        """
        关闭浏览器
        """
        try:
            browser.quit()
            time.sleep(1)
            service.stop()
        except:
            pass
        finally:
            self.browser = None
            self.service = None

    def release(self):
        # 删除chrome data文件夹
        os.path.exists(self.userdata_file_path) and shutil.rmtree(self.userdata_file_path)
        # 关闭chrome
        self.service and self.browser and self.close_browser(self.service, self.browser)
        # 关闭浏览器进程
        self.browser_id and os.kill(self.browser_id, 15)
        # 关闭chrome
        for process in psutil.process_iter(['pid', 'name']):
            if process.info['name'] in ['chromedriver', *CHROME_NAME]:
                try:
                    process.terminate()  # 终止进程
                    process.wait(timeout=2)  # 等待进程终止
                except: pass

    def __del__(self):
        self.release()
