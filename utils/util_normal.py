# -*- coding: utf-8 -*-
"""
通用小工具
"""
import re
import os
import json
import base64
import random
import string
import hashlib
import zipfile
from hashlib import md5

import requests
from flask import make_response, jsonify

from conf import PROJECT_PATH, DEVELOPER_NOTIFY_API, SERVER_ENV, BOOKING_GROUP_API
from utils.util_exceptions import RequestErr


import urllib3
from urllib.parse import urlencode, quote
from retry.api import retry_call

urllib3.disable_warnings()


def set_pid(pid_key):
    """设置pid"""
    if pid_key:
        pid_dict = get_pid_dict()
        pid_dict[pid_key.lower()] = int(os.getpid())

        with open(os.path.join(PROJECT_PATH, "docs", "pid.json"), "w") as f:
            json.dump(pid_dict, f)
        return True
    else:
        return False


def get_md5(txt):
    """md5"""
    return md5(str(txt).encode("utf-8")).hexdigest()


def getUrl(ip, port, more):
    """拼接url"""
    return f"http://{ip}:{port}{more}"


def get_randome_keys(length=8):
    """获取随机字符串"""
    a = string.ascii_letters + string.digits
    key = random.sample(a, length)
    keys = "".join(key)
    return keys


def unzip_file(from_file, target_dir):
    """
    解压文件
    :param from_file: 需要解压的文件全路径
    :param target_dir: 目标文件夹
    :return:
    """
    if zipfile.is_zipfile(from_file):
        fz = zipfile.ZipFile(from_file, 'r')
        for file in fz.namelist():
            fz.extract(file, target_dir)
        fz.close()
    else:
        print(f'{from_file} is not zip')


def listdir_nohidden(path):
    """
    忽略隐藏文件
    """
    for f in os.listdir(path):
        if not f.startswith('.'):
            yield f


def check(html, json=True):
    """检查网页状态的方法"""
    code = html.status_code
    if code == 200:
        if html.text == "ok":
            return True
        if json:
            try:
                return html.json()
            except:
                pass
        return html.text
    else:
        print(html.text)
        raise RequestErr(code)


def get_pid_dict():
    with open(os.path.join(PROJECT_PATH, "docs", "pid.json"), "r+") as f:
        pid_dict = json.load(f)
    return pid_dict


def get_json_response(res_dict: dict):
    """
    传入字典，获取响应
    应当处理传入的res_dict中值的格式，防止无法序列化
    """
    res = make_response(jsonify(res_dict))
    res.content_type += "; charset=utf-8"
    return res


def find_chinese(file):
    pattern = re.compile(r'[^\u4e00-\u9fa5]')
    chinese = re.sub(pattern, '', file)
    return chinese


def find_unchinese(file):
    pattern = re.compile(r'[\u4e00-\u9fa5]')
    unchinese = re.sub(pattern, "", file)
    return unchinese


def remove_html(astr):
    dr = re.compile(r'<[^>]+>', re.S)
    dd = dr.sub('', astr)
    return dd


def insert_enter_to_string(astr, rows_count, every=35):
    """
    修改字符串的格式
    astr: 字符串
    rows_count: 行数
    every: 每行的字数
    """
    astr = astr.replace('\n', '').replace(' ', '').replace('\xa0', ' ')
    astr = re.sub('\\s+', ' ', astr)
    new_str = ''
    for i in range(0, len(astr), every):
        new_str += astr[i:i + every:] + '\n'
        if new_str.strip('\n').count('\n') == rows_count - 1:
            break
    return new_str.strip('\n')


def compute_duration_time(start_time, end_time) -> str:
    """
    计算本次运行时间
    :param start_time: 开始时间
    :param end_time: 结束时间
    :return: 返回当前的运行时长
    """
    duration_time = end_time - start_time
    hour, minute = divmod(duration_time, 3600)
    minute, second = divmod(minute, 60)
    duration_time_str = f"{int(hour)} 小时 {int(minute)} 分钟 {int(second)} 秒"
    return duration_time_str


def send_request(url, *, method, **kwargs):
    """
    封装请求函数
    :param url: 请求的url
    :param method: 请求的类型
    :param kwargs: 传入的参数数据
    :return: 返回响应对象
    """
    data = kwargs.get("data", {})
    files = kwargs.get("files", [])
    mask = kwargs.get("mask", False)
    params = kwargs.get("params", {})
    timeout = kwargs.get("timeout", 10)
    cookies = kwargs.get("cookies", {})
    headers = kwargs.get("headers", {})
    allow_redirects = kwargs.get("allow_redirects", True)
    topic = kwargs.get("topic", "")  # 文本类型
    proxy = kwargs.get("proxy", {})
    proxy = proxy if proxy else ""

    final_headers = {
        "content-type": {"json": "application/json; charset=UTF-8",
                         "form": "application/x-www-form-urlencoded; charset=UTF-8",
                         "text": "text/html; charset=UTF-8"}.get(topic),
        "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.0.0 Safari/537.36",
        "connection": "close"
    }
    if not topic: final_headers.pop("content-type")
    final_headers.update(headers)
    url = "?".join([url, urlencode(params)]) if params and isinstance(params, dict) else url
    url = "?".join([url, quote(params)]) if params and isinstance(params, str) else url
    data = json.dumps(data) if data and "json" == topic else data
    data = urlencode(data) if data and "form" == topic else data
    func = getattr(requests, method)
    response = func(url=url, headers=final_headers, data=data, proxies=proxy, cookies=cookies, timeout=(timeout, 10), files=files, verify=False, allow_redirects=allow_redirects)
    return response


def send_message_to_wechat(wx_api: str, message: any, contact=None) -> bool:
    """
    发送查询信息到企业微信
    :param wx_api: 需要发送的企业微信的api
    :param contact: 设置是否发送信息到企业微信中需要通知的人
    :param message: 需要发送的信息
    :return: 成功 True, 失败 False
    """
    if contact is None:
        contact = list()

    kw = {
        "data": {
            "msgtype": "text",
            "text": {
                "content": message,
                "mentioned_mobile_list": contact
            }
        },
        "method": "post",
        "topic": "json"
    }

    if isinstance(message, bytes):
        md5_value = hashlib.md5()
        md5_value.update(message)
        kw.update({
            "data": {
                "msgtype": "image",
                "image": {
                    "base64": str(base64.b64encode(message), 'utf-8'),
                    "md5": md5_value.hexdigest()
                }
            }
        })

    response = retry_call(send_request, [wx_api], kw, tries=1)
    if response.status_code == 200:
        return True
    return False


# 向爬虫组发送信息
def send_message_to_developer(message: str, contact: list):
    message = SERVER_ENV + ": booking-automatic-spider\n" + message
    send_message_to_wechat(wx_api=DEVELOPER_NOTIFY_API, message=message, contact=contact)


# 给订舱群组发送信息
def send_message_to_group(message: str, contact: list):
    send_message_to_wechat(wx_api=BOOKING_GROUP_API, message=message, contact=contact)