# -*- coding: utf-8 -*-
import os
from urllib.parse import unquote

from gunicorn_log import <PERSON><PERSON><PERSON><PERSON><PERSON>
from conf import SERVER_ENV, PROJECT_PATH

loglevel = "INFO"
bind = "0.0.0.0:8090"
worker_class = "tornado"
workers = 1 if SERVER_ENV not in ["local", "dev", "beta"] else 1  # 启动的进程数
x_forwarded_for_header = "X-FORWARDED-FOR"

pidfile = os.path.join(PROJECT_PATH, "docs", "gunicorn.pid")

access_log_format = "%(h)s %(l)s '%(r)s' %(s)s %(L)s"

logger_class = GunicornLogger


def pre_request(worker, req):
    # 将log日志输出显示中文
    req.uri = unquote(req.uri)
