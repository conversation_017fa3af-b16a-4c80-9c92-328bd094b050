# 自动化订舱

### 关闭航华订舱-20230816

### 一、使用请求触发的应用

1. check_booking-info.py

   > CGM网站已放仓"CMA", "CNC", "CST", "ANL"船司的预配箱信息进行查询

   启动：get请求

   - 请求路径：`check-booking`
   - 请求参数：name=info

   关闭：post请求

   - 请求路径：`check-booking`
   - 请求体：表单格式name=info

2. check_booking-state.py

   > CGM网站已定"CMA", "CNC", "CST", "ANL"船司的订单状态查询

   启动：get请求

   - 请求路径：`check-booking`
   - 请求参数：name=state

   关闭：post请求

   - 请求路径：`check-booking`
   - 请求体：表单格式name=state

3. bill_no_query.py

   > 更新inttra提单号

   启动：get请求

   - 请求路径：`/update-billNo`

### 二、使用RocketMQ消息启动的应用

1. booking_start.py

   > 接收mq信息，并把信息进行分配

- inttra订舱
  可支持的船公司：CMA、CNC、ANL、MSC
- inttra订舱整理

| 用户名             | 密码                | 特殊注意事项                                                 |
| ------------------ | ------------------- | ------------------------------------------------------------ |
| <EMAIL>  | suzhouHGJyunlsp@123 |                                                              |
| <EMAIL>  | HGJYunlsp123!@      |                                                              |
| <EMAIL>  | Yunlsp123!          |                                                              |
| <EMAIL> | Samsung21!          | 不处理海运附加费                                             |
| <EMAIL> | Szhgj123@           | 当enterpriseId为1867089026105733121，船公司为cma，设置forwarder为：SHANGHAI PANMAX INT'L LOGISTICS CO.,LTD |


### cma配舱回单上传接口

- `/getAttachment`
  - 过滤数据，调度下载，每半小时进行调用一次， 查询的单号范围是从当前时间开始算，两个小时以内，调用`booking-cma-upload-bc-spider`
- `/updateBillNoState`
  - 更新单号数据和状态，调用服务下载附件之后更新数据
- `/uploadBC`
  - 每隔一个小时上传一次当天的bc，补偿机制